import React, { useEffect, useState } from 'react';
import { X, AlertTriangle, Megaphone } from 'lucide-react';

interface EmergencyAlertProps {
  message: string;
  type: 'emergency' | 'announcement';
  onClose: () => void;
}

const EmergencyAlert: React.FC<EmergencyAlertProps> = ({ message, type, onClose }) => {
  const [countdown, setCountdown] = useState(15);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          onClose();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [onClose]);

  const bgColor = type === 'emergency' ? 'bg-red-600' : 'bg-orange-600';
  const Icon = type === 'emergency' ? AlertTriangle : Megaphone;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 animate-fadeIn">
      <div className={`${bgColor} text-white p-8 rounded-2xl max-w-4xl mx-4 shadow-2xl animate-slideInUp`}>
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-center">
            <Icon className="w-8 h-8 ml-4 animate-pulse" />
            <h2 className="text-3xl font-bold">
              {type === 'emergency' ? 'تنبيه طوارئ' : 'إعلان هام'}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-white/80 hover:text-white transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>
        
        <div className="text-center">
          <p className="text-2xl leading-relaxed mb-6 font-medium">
            {message}
          </p>
          
          <div className="flex justify-center items-center space-x-4 space-x-reverse">
            <div className="bg-white/20 px-4 py-2 rounded-lg">
              <span className="text-sm">سيتم إغلاق الإعلان خلال</span>
              <span className="text-2xl font-bold mx-2">{countdown}</span>
              <span className="text-sm">ثانية</span>
            </div>
            <button
              onClick={onClose}
              className="bg-white/20 hover:bg-white/30 text-white px-6 py-2 rounded-lg transition-colors"
            >
              إغلاق الآن
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmergencyAlert;