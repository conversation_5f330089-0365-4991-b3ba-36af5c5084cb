import React, { useState } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { Book, Settings } from 'lucide-react';
import DisplayBoard from '../components/DisplayBoard';
import AdminPanel from '../components/AdminPanel';
import EmergencyAlert from '../components/EmergencyAlert';
import LoginForm from '../components/LoginForm';
import { useAuth } from '../hooks/useAuth';

const AdminPage: React.FC = () => {
  const { mosqueSlug } = useParams<{ mosqueSlug: string }>();
  const { isAuthenticated, login } = useAuth(mosqueSlug);
  const [currentView, setCurrentView] = useState<'display' | 'admin'>('display');
  const [emergencyAlert, setEmergencyAlert] = useState<{
    show: boolean;
    message: string;
    type: 'emergency' | 'announcement';
  }>({
    show: false,
    message: '',
    type: 'announcement'
  });

  // التحقق من وجود معرف المسجد
  if (!mosqueSlug) {
    return <Navigate to="/" replace />;
  }

  // إذا لم يكن مسجل دخول، عرض نموذج تسجيل الدخول
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-emerald-900 flex items-center justify-center p-4" dir="rtl">
        <div className="w-full max-w-md">
          {/* Logo and Title */}
          <div className="text-center mb-8">
            <div className="bg-emerald-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
              <Book className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">لوحة تحكم المسجد</h1>
            <p className="text-slate-300">تسجيل الدخول إلى لوحة التحكم</p>
            <p className="text-emerald-400 text-sm mt-2">المسجد: {mosqueSlug}</p>
          </div>

          <LoginForm onLogin={login} />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white" dir="rtl">
      {/* Emergency Alert Overlay */}
      {emergencyAlert.show && (
        <EmergencyAlert
          message={emergencyAlert.message}
          type={emergencyAlert.type}
          onClose={() => setEmergencyAlert({ ...emergencyAlert, show: false })}
        />
      )}

      {/* Navigation Bar */}
      <nav className="bg-slate-800 border-b border-slate-700 px-6 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="bg-emerald-600 p-2 rounded-lg">
              <Book className="w-6 h-6" />
            </div>
            <div>
              <h1 className="text-xl font-bold">لوحة تحكم المسجد</h1>
              <p className="text-slate-400 text-sm">المسجد: {mosqueSlug}</p>
            </div>
          </div>
          <div className="flex space-x-2 space-x-reverse">
            <button
              onClick={() => setCurrentView('display')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                currentView === 'display' 
                  ? 'bg-emerald-600 text-white' 
                  : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
              }`}
            >
              لوحة العرض
            </button>
            <button
              onClick={() => setCurrentView('admin')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                currentView === 'admin' 
                  ? 'bg-emerald-600 text-white' 
                  : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
              }`}
            >
              <Settings className="w-4 h-4 inline ml-2" />
              لوحة التحكم
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex-1">
        {currentView === 'display' ? (
          <DisplayBoard />
        ) : (
          <AdminPanel 
            onEmergencyAlert={(message, type) => 
              setEmergencyAlert({ show: true, message, type })
            }
          />
        )}
      </main>
    </div>
  );
};

export default AdminPage;
