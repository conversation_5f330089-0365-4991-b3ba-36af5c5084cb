import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Book, Plus, Search, Building, Users, Calendar, Loader } from 'lucide-react';
import { useMosques } from '../hooks/useMosques';
import { Mosque } from '../types';

const HomePage: React.FC = () => {
  const { mosques, loading, error, addMosque } = useMosques();
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [addingMosque, setAddingMosque] = useState(false);
  const [newMosque, setNewMosque] = useState({
    name: '',
    address: '',
    phone: ''
  });

  const filteredMosques = mosques.filter(mosque =>
    mosque.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (mosque.address && mosque.address.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleAddMosque = async () => {
    if (newMosque.name.trim()) {
      setAddingMosque(true);
      try {
        const mosqueId = newMosque.name
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^\w\-]/g, '');

        const mosque: Omit<Mosque, 'createdAt'> = {
          id: mosqueId,
          name: newMosque.name,
          capacity: 500,
          mushafs: 150,
          worshippers: 320,
          area: 800,
          rows: 25,
          address: newMosque.address,
          phone: newMosque.phone,
          logo: '',
          backgroundImage: ''
        };

        await addMosque(mosque);
        setNewMosque({ name: '', address: '', phone: '' });
        setShowAddForm(false);
      } catch (err) {
        alert('حدث خطأ في إضافة المسجد. حاول مرة أخرى.');
      } finally {
        setAddingMosque(false);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-emerald-900 text-white" dir="rtl">
      {/* Header */}
      <header className="bg-slate-800/50 backdrop-blur-lg border-b border-slate-700">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center">
            <div className="bg-emerald-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
              <Book className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl font-bold mb-4">نظام إعلانات المساجد الرقمي</h1>
            <p className="text-xl text-slate-300 mb-8">منصة شاملة لإدارة وعرض إعلانات المساجد</p>
            
            {/* Search */}
            <div className="max-w-md mx-auto relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
              <input
                type="text"
                placeholder="البحث عن مسجد..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-12 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-emerald-500"
              />
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-6 py-12">
        {/* Add Mosque Button */}
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl font-bold">المساجد المسجلة</h2>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg transition-colors flex items-center space-x-2 space-x-reverse"
          >
            <Plus className="w-5 h-5" />
            <span>إضافة مسجد جديد</span>
          </button>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <Loader className="w-8 h-8 animate-spin text-emerald-400" />
            <span className="mr-3 text-slate-300">جاري تحميل المساجد...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6">
            <p className="text-red-200">خطأ: {error}</p>
          </div>
        )}

        {/* Mosques Grid */}
        {!loading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMosques.map((mosque) => (
            <div key={mosque.id} className="bg-slate-800/50 backdrop-blur-lg rounded-2xl p-6 shadow-2xl border border-slate-700 hover:border-emerald-500 transition-colors">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  {mosque.logo ? (
                    <img src={mosque.logo} alt="شعار المسجد" className="w-12 h-12 rounded-full" />
                  ) : (
                    <div className="bg-emerald-600 p-3 rounded-full">
                      <Building className="w-6 h-6 text-white" />
                    </div>
                  )}
                  <div>
                    <h3 className="text-xl font-bold">{mosque.name}</h3>
                    <p className="text-slate-400 text-sm">معرف: {mosque.id}</p>
                  </div>
                </div>
              </div>

              {mosque.address && (
                <p className="text-slate-300 text-sm mb-4">{mosque.address}</p>
              )}

              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-emerald-400 text-lg font-bold">{mosque.capacity}</div>
                  <div className="text-slate-400 text-xs">طاقة المصلين</div>
                </div>
                <div className="text-center">
                  <div className="text-emerald-400 text-lg font-bold">{mosque.worshippers}</div>
                  <div className="text-slate-400 text-xs">عدد المأمومين</div>
                </div>
              </div>

              <div className="space-y-3">
                <Link
                  to={`/${mosque.id}`}
                  className="block w-full bg-emerald-600 hover:bg-emerald-700 text-white text-center py-2 rounded-lg transition-colors"
                >
                  عرض لوحة الإعلانات
                </Link>
                <Link
                  to={`/admin/${mosque.id}`}
                  className="block w-full bg-slate-700 hover:bg-slate-600 text-white text-center py-2 rounded-lg transition-colors"
                >
                  لوحة التحكم
                </Link>
              </div>
            </div>
          ))}
          </div>
        )}

        {!loading && filteredMosques.length === 0 && (
          <div className="text-center py-12">
            <Building className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-slate-300 mb-2">لا توجد مساجد</h3>
            <p className="text-slate-400">
              {searchTerm ? 'لم يتم العثور على مساجد تطابق البحث' : 'لم يتم إضافة أي مساجد بعد'}
            </p>
          </div>
        )}

        {/* Add Mosque Modal */}
        {showAddForm && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-slate-800 rounded-2xl p-6 w-full max-w-md">
              <h3 className="text-xl font-bold mb-6">إضافة مسجد جديد</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-slate-300 text-sm font-medium mb-2">
                    اسم المسجد *
                  </label>
                  <input
                    type="text"
                    value={newMosque.name}
                    onChange={(e) => setNewMosque({ ...newMosque, name: e.target.value })}
                    className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"
                    placeholder="أدخل اسم المسجد"
                  />
                </div>
                
                <div>
                  <label className="block text-slate-300 text-sm font-medium mb-2">
                    العنوان
                  </label>
                  <input
                    type="text"
                    value={newMosque.address}
                    onChange={(e) => setNewMosque({ ...newMosque, address: e.target.value })}
                    className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"
                    placeholder="أدخل عنوان المسجد"
                  />
                </div>
                
                <div>
                  <label className="block text-slate-300 text-sm font-medium mb-2">
                    رقم الهاتف
                  </label>
                  <input
                    type="tel"
                    value={newMosque.phone}
                    onChange={(e) => setNewMosque({ ...newMosque, phone: e.target.value })}
                    className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"
                    placeholder="أدخل رقم الهاتف"
                  />
                </div>
              </div>

              <div className="flex space-x-4 space-x-reverse mt-6">
                <button
                  onClick={() => setShowAddForm(false)}
                  className="flex-1 bg-slate-700 hover:bg-slate-600 text-white py-2 rounded-lg transition-colors"
                >
                  إلغاء
                </button>
                <button
                  onClick={handleAddMosque}
                  disabled={!newMosque.name.trim() || addingMosque}
                  className="flex-1 bg-emerald-600 hover:bg-emerald-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white py-2 rounded-lg transition-colors flex items-center justify-center"
                >
                  {addingMosque ? (
                    <>
                      <Loader className="w-4 h-4 animate-spin ml-2" />
                      جاري الإضافة...
                    </>
                  ) : (
                    'إضافة'
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-slate-800/50 backdrop-blur-lg border-t border-slate-700 mt-12">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-slate-400">
            <p className="mb-4">نظام إعلانات المساجد الرقمي</p>
            <div className="flex justify-center space-x-8 space-x-reverse text-sm">
              <span>✨ سهل الاستخدام</span>
              <span>🔒 آمن ومحمي</span>
              <span>📱 متجاوب مع جميع الأجهزة</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;
