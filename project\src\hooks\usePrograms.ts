import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { Program } from '../types';

export function usePrograms(mosqueId: string) {
  const [programs, setPrograms] = useState<Program[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // جلب برامج مسجد معين
  const fetchPrograms = async () => {
    if (!mosqueId) return;
    
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('programs')
        .select('*')
        .eq('mosque_id', mosqueId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const formattedPrograms: Program[] = data.map(program => ({
        id: program.id,
        mosqueId: program.mosque_id,
        name: program.name,
        lecturer: program.lecturer,
        level: program.level,
        attendance: program.attendance,
        timeFrom: program.time_from,
        timeTo: program.time_to,
        status: program.status,
        type: program.type,
        notes: program.notes,
        hijriDate: program.hijri_date,
        gregorianDate: program.gregorian_date,
        days: program.days
      }));

      setPrograms(formattedPrograms);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ في جلب البرامج');
      console.error('Error fetching programs:', err);
    } finally {
      setLoading(false);
    }
  };

  // إضافة برنامج جديد
  const addProgram = async (program: Omit<Program, 'id'>) => {
    try {
      const { data, error } = await supabase
        .from('programs')
        .insert([{
          mosque_id: program.mosqueId,
          name: program.name,
          lecturer: program.lecturer,
          level: program.level,
          attendance: program.attendance,
          time_from: program.timeFrom,
          time_to: program.timeTo,
          status: program.status,
          type: program.type,
          notes: program.notes,
          hijri_date: program.hijriDate,
          gregorian_date: program.gregorianDate,
          days: program.days
        }])
        .select()
        .single();

      if (error) throw error;

      const newProgram: Program = {
        id: data.id,
        mosqueId: data.mosque_id,
        name: data.name,
        lecturer: data.lecturer,
        level: data.level,
        attendance: data.attendance,
        timeFrom: data.time_from,
        timeTo: data.time_to,
        status: data.status,
        type: data.type,
        notes: data.notes,
        hijriDate: data.hijri_date,
        gregorianDate: data.gregorian_date,
        days: data.days
      };

      setPrograms(prev => [newProgram, ...prev]);
      return newProgram;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ في إضافة البرنامج');
      console.error('Error adding program:', err);
      throw err;
    }
  };

  // تحديث برنامج
  const updateProgram = async (id: string, updates: Partial<Program>) => {
    try {
      const { data, error } = await supabase
        .from('programs')
        .update({
          name: updates.name,
          lecturer: updates.lecturer,
          level: updates.level,
          attendance: updates.attendance,
          time_from: updates.timeFrom,
          time_to: updates.timeTo,
          status: updates.status,
          type: updates.type,
          notes: updates.notes,
          hijri_date: updates.hijriDate,
          gregorian_date: updates.gregorianDate,
          days: updates.days
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      const updatedProgram: Program = {
        id: data.id,
        mosqueId: data.mosque_id,
        name: data.name,
        lecturer: data.lecturer,
        level: data.level,
        attendance: data.attendance,
        timeFrom: data.time_from,
        timeTo: data.time_to,
        status: data.status,
        type: data.type,
        notes: data.notes,
        hijriDate: data.hijri_date,
        gregorianDate: data.gregorian_date,
        days: data.days
      };

      setPrograms(prev => prev.map(p => p.id === id ? updatedProgram : p));
      return updatedProgram;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ في تحديث البرنامج');
      console.error('Error updating program:', err);
      throw err;
    }
  };

  // حذف برنامج
  const deleteProgram = async (id: string) => {
    try {
      const { error } = await supabase
        .from('programs')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setPrograms(prev => prev.filter(p => p.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ في حذف البرنامج');
      console.error('Error deleting program:', err);
      throw err;
    }
  };

  useEffect(() => {
    fetchPrograms();
  }, [mosqueId]);

  return {
    programs,
    loading,
    error,
    addProgram,
    updateProgram,
    deleteProgram,
    refetch: fetchPrograms
  };
}
