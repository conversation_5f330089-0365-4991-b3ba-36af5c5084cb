import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Plus, Edit, Trash2, Save, X, AlertTriangle, Megaphone, LogOut, Building } from 'lucide-react';
import { useLocalStorage } from '../hooks/useLocalStorage';
import { useAuth } from '../hooks/useAuth';
import { Program, Mosque } from '../types';
import ImageUpload from './ImageUpload';

interface AdminPanelProps {
  onEmergencyAlert: (message: string, type: 'emergency' | 'announcement') => void;
}

const AdminPanel: React.FC<AdminPanelProps> = ({ onEmergencyAlert }) => {
  const { mosqueSlug } = useParams<{ mosqueSlug: string }>();
  const { currentUser, logout } = useAuth(mosqueSlug);
  const [activeTab, setActiveTab] = useState<'mosque' | 'programs' | 'emergency'>('mosque');

  // البحث عن المسجد بناءً على الـ slug
  const [mosques] = useLocalStorage<Mosque[]>('mosques', []);
  const currentMosque = mosques.find(m => m.id === mosqueSlug);
  const mosqueId = mosqueSlug || 'default';

  // استخدام localStorage مع معرف المسجد
  const [mosqueData, setMosqueData] = useLocalStorage<Mosque>(`mosque-${mosqueId}`, 
    currentMosque || {
      id: mosqueId,
      name: 'مسجد جديد',
      capacity: 500,
      mushafs: 150,
      worshippers: 320,
      area: 800,
      rows: 25,
      address: '',
      phone: '',
      createdAt: new Date().toISOString()
    }
  );

  const [programs, setPrograms] = useLocalStorage<Program[]>(`programs-${mosqueId}`, []);
  const [allMosques, setAllMosques] = useLocalStorage<Mosque[]>('mosques', []);

  const [editingProgram, setEditingProgram] = useState<Program | null>(null);
  const [emergencyMessage, setEmergencyMessage] = useState('');
  const [emergencyType, setEmergencyType] = useState<'emergency' | 'announcement'>('announcement');

  const handleSaveMosqueData = () => {
    // تحديث بيانات المسجد في قائمة المساجد العامة
    const updatedMosques = allMosques.map(m => m.id === mosqueId ? mosqueData : m);
    if (!allMosques.find(m => m.id === mosqueId)) {
      updatedMosques.push(mosqueData);
    }
    setAllMosques(updatedMosques);
    alert('تم حفظ بيانات المسجد بنجاح');
  };

  const handleAddProgram = () => {
    const newProgram: Program = {
      id: Date.now().toString(),
      mosqueId: mosqueId,
      name: 'برنامج جديد',
      lecturer: '',
      level: 'مبتدئ',
      attendance: 0,
      timeFrom: '09:00',
      timeTo: '10:00',
      status: 'نشط',
      type: 'عام',
      notes: '',
      hijriDate: new Date().toLocaleDateString('ar-SA-u-ca-islamic'),
      gregorianDate: new Date().toLocaleDateString('ar-SA'),
      days: ['الأحد']
    };
    setPrograms([...programs, newProgram]);
    setEditingProgram(newProgram);
  };

  const handleEditProgram = (program: Program) => {
    setEditingProgram({ ...program });
  };

  const handleSaveProgram = () => {
    if (editingProgram) {
      const updatedPrograms = programs.map(p => 
        p.id === editingProgram.id ? editingProgram : p
      );
      setPrograms(updatedPrograms);
      setEditingProgram(null);
      alert('تم حفظ البرنامج بنجاح');
    }
  };

  const handleDeleteProgram = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا البرنامج؟')) {
      setPrograms(programs.filter(p => p.id !== id));
      alert('تم حذف البرنامج بنجاح');
    }
  };

  const handleSendEmergencyAlert = () => {
    if (emergencyMessage.trim()) {
      onEmergencyAlert(emergencyMessage, emergencyType);
      setEmergencyMessage('');
      alert('تم إرسال الإعلان العاجل');
    }
  };

  if (!currentUser || !currentMosque) {
    return <div>جاري التحميل...</div>;
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-4 space-x-reverse">
          <div className="bg-emerald-600 p-2 rounded-lg">
            <Building className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-white">{mosqueData.name}</h1>
            <p className="text-slate-400">مرحباً، {currentUser.username}</p>
          </div>
        </div>
        <button
          onClick={logout}
          className="flex items-center space-x-2 space-x-reverse bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          <LogOut className="w-4 h-4" />
          <span>تسجيل الخروج</span>
        </button>
      </div>

      {/* Tabs */}
      <div className="flex space-x-4 space-x-reverse mb-6 border-b border-slate-700">
        <button
          onClick={() => setActiveTab('mosque')}
          className={`px-6 py-3 font-medium transition-colors ${
            activeTab === 'mosque'
              ? 'text-emerald-400 border-b-2 border-emerald-400'
              : 'text-slate-400 hover:text-white'
          }`}
        >
          بيانات المسجد
        </button>
        <button
          onClick={() => setActiveTab('programs')}
          className={`px-6 py-3 font-medium transition-colors ${
            activeTab === 'programs'
              ? 'text-emerald-400 border-b-2 border-emerald-400'
              : 'text-slate-400 hover:text-white'
          }`}
        >
          إدارة البرامج
        </button>
        <button
          onClick={() => setActiveTab('emergency')}
          className={`px-6 py-3 font-medium transition-colors ${
            activeTab === 'emergency'
              ? 'text-emerald-400 border-b-2 border-emerald-400'
              : 'text-slate-400 hover:text-white'
          }`}
        >
          الإعلانات العاجلة
        </button>
      </div>

      {/* Mosque Data Tab */}
      {activeTab === 'mosque' && (
        <div className="bg-slate-800 rounded-xl p-6 shadow-xl">
          <h2 className="text-2xl font-bold text-white mb-6">بيانات المسجد</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            {/* Logo Upload */}
            <div className="lg:col-span-1">
              <ImageUpload
                currentImage={mosqueData.logo}
                onImageChange={(imageUrl) => setMosqueData({ ...mosqueData, logo: imageUrl })}
                label="شعار المسجد"
              />
            </div>
            
            {/* Background Image Upload */}
            <div className="lg:col-span-2">
              <ImageUpload
                currentImage={mosqueData.backgroundImage}
                onImageChange={(imageUrl) => setMosqueData({ ...mosqueData, backgroundImage: imageUrl })}
                label="صورة خلفية للمسجد"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-slate-300 text-sm font-medium mb-2">
                اسم المسجد
              </label>
              <input
                type="text"
                value={mosqueData.name}
                onChange={(e) => setMosqueData({ ...mosqueData, name: e.target.value })}
                className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-slate-300 text-sm font-medium mb-2">
                العنوان
              </label>
              <input
                type="text"
                value={mosqueData.address || ''}
                onChange={(e) => setMosqueData({ ...mosqueData, address: e.target.value })}
                className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-slate-300 text-sm font-medium mb-2">
                رقم الهاتف
              </label>
              <input
                type="tel"
                value={mosqueData.phone || ''}
                onChange={(e) => setMosqueData({ ...mosqueData, phone: e.target.value })}
                className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-slate-300 text-sm font-medium mb-2">
                طاقة المصلين
              </label>
              <input
                type="number"
                value={mosqueData.capacity}
                onChange={(e) => setMosqueData({ ...mosqueData, capacity: parseInt(e.target.value) || 0 })}
                className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-slate-300 text-sm font-medium mb-2">
                عدد المصاحف
              </label>
              <input
                type="number"
                value={mosqueData.mushafs}
                onChange={(e) => setMosqueData({ ...mosqueData, mushafs: parseInt(e.target.value) || 0 })}
                className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-slate-300 text-sm font-medium mb-2">
                عدد المأمومين
              </label>
              <input
                type="number"
                value={mosqueData.worshippers}
                onChange={(e) => setMosqueData({ ...mosqueData, worshippers: parseInt(e.target.value) || 0 })}
                className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-slate-300 text-sm font-medium mb-2">
                مساحة المسجد (متر مربع)
              </label>
              <input
                type="number"
                value={mosqueData.area}
                onChange={(e) => setMosqueData({ ...mosqueData, area: parseInt(e.target.value) || 0 })}
                className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-slate-300 text-sm font-medium mb-2">
                عدد الصفوف
              </label>
              <input
                type="number"
                value={mosqueData.rows}
                onChange={(e) => setMosqueData({ ...mosqueData, rows: parseInt(e.target.value) || 0 })}
                className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="mt-6">
            <button
              onClick={handleSaveMosqueData}
              className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2 rounded-lg transition-colors"
            >
              <Save className="w-4 h-4 inline ml-2" />
              حفظ البيانات
            </button>
          </div>
        </div>
      )}

      {/* Programs Management Tab */}
      {activeTab === 'programs' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-white">إدارة البرامج</h2>
            <button
              onClick={handleAddProgram}
              className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4 inline ml-2" />
              إضافة برنامج
            </button>
          </div>

          <div className="bg-slate-800 rounded-xl p-6 shadow-xl">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="bg-slate-700 text-slate-300">
                    <th className="p-3 text-right">اسم البرنامج</th>
                    <th className="p-3 text-right">المحاضر</th>
                    <th className="p-3 text-right">المستوى</th>
                    <th className="p-3 text-right">الوقت</th>
                    <th className="p-3 text-right">الحضور</th>
                    <th className="p-3 text-right">الحالة</th>
                    <th className="p-3 text-right">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {programs.map((program) => (
                    <tr key={program.id} className="border-b border-slate-600">
                      <td className="p-3 text-white">{program.name}</td>
                      <td className="p-3 text-slate-300">{program.lecturer}</td>
                      <td className="p-3 text-slate-300">{program.level}</td>
                      <td className="p-3 text-slate-300">{program.timeFrom} - {program.timeTo}</td>
                      <td className="p-3 text-slate-300">{program.attendance}</td>
                      <td className="p-3">
                        <span className="bg-green-600 text-white px-2 py-1 rounded text-xs">
                          {program.status}
                        </span>
                      </td>
                      <td className="p-3">
                        <div className="flex space-x-2 space-x-reverse">
                          <button
                            onClick={() => handleEditProgram(program)}
                            className="text-blue-400 hover:text-blue-300"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteProgram(program.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Edit Program Modal */}
          {editingProgram && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-slate-800 rounded-xl p-6 w-full max-w-2xl m-4 max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-xl font-bold text-white">تعديل البرنامج</h3>
                  <button
                    onClick={() => setEditingProgram(null)}
                    className="text-slate-400 hover:text-white"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-slate-300 text-sm font-medium mb-2">
                      اسم البرنامج
                    </label>
                    <input
                      type="text"
                      value={editingProgram.name}
                      onChange={(e) => setEditingProgram({ ...editingProgram, name: e.target.value })}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"
                    />
                  </div>
                  <div>
                    <label className="block text-slate-300 text-sm font-medium mb-2">
                      المحاضر
                    </label>
                    <input
                      type="text"
                      value={editingProgram.lecturer}
                      onChange={(e) => setEditingProgram({ ...editingProgram, lecturer: e.target.value })}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"
                    />
                  </div>
                  <div>
                    <label className="block text-slate-300 text-sm font-medium mb-2">
                      المستوى
                    </label>
                    <select
                      value={editingProgram.level}
                      onChange={(e) => setEditingProgram({ ...editingProgram, level: e.target.value })}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"
                    >
                      <option value="مبتدئ">مبتدئ</option>
                      <option value="متوسط">متوسط</option>
                      <option value="متقدم">متقدم</option>
                      <option value="جميع المستويات">جميع المستويات</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-slate-300 text-sm font-medium mb-2">
                      عدد الحضور
                    </label>
                    <input
                      type="number"
                      value={editingProgram.attendance}
                      onChange={(e) => setEditingProgram({ ...editingProgram, attendance: parseInt(e.target.value) || 0 })}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"
                    />
                  </div>
                  <div>
                    <label className="block text-slate-300 text-sm font-medium mb-2">
                      وقت البداية
                    </label>
                    <input
                      type="time"
                      value={editingProgram.timeFrom}
                      onChange={(e) => setEditingProgram({ ...editingProgram, timeFrom: e.target.value })}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"
                    />
                  </div>
                  <div>
                    <label className="block text-slate-300 text-sm font-medium mb-2">
                      وقت النهاية
                    </label>
                    <input
                      type="time"
                      value={editingProgram.timeTo}
                      onChange={(e) => setEditingProgram({ ...editingProgram, timeTo: e.target.value })}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-slate-300 text-sm font-medium mb-2">
                      ملاحظات
                    </label>
                    <textarea
                      value={editingProgram.notes}
                      onChange={(e) => setEditingProgram({ ...editingProgram, notes: e.target.value })}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"
                      rows={3}
                    />
                  </div>
                </div>
                <div className="flex justify-end space-x-4 space-x-reverse mt-6">
                  <button
                    onClick={() => setEditingProgram(null)}
                    className="px-4 py-2 text-slate-400 hover:text-white transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={handleSaveProgram}
                    className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2 rounded-lg transition-colors"
                  >
                    حفظ التغييرات
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Emergency Alerts Tab */}
      {activeTab === 'emergency' && (
        <div className="bg-slate-800 rounded-xl p-6 shadow-xl">
          <h2 className="text-2xl font-bold text-white mb-6">الإعلانات العاجلة</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-slate-300 text-sm font-medium mb-2">
                نوع الإعلان
              </label>
              <select
                value={emergencyType}
                onChange={(e) => setEmergencyType(e.target.value as 'emergency' | 'announcement')}
                className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"
              >
                <option value="announcement">إعلان عام</option>
                <option value="emergency">حالة طوارئ</option>
              </select>
            </div>
            <div>
              <label className="block text-slate-300 text-sm font-medium mb-2">
                نص الإعلان
              </label>
              <textarea
                value={emergencyMessage}
                onChange={(e) => setEmergencyMessage(e.target.value)}
                placeholder="اكتب نص الإعلان العاجل هنا..."
                className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"
                rows={4}
              />
            </div>
            <button
              onClick={handleSendEmergencyAlert}
              disabled={!emergencyMessage.trim()}
              className={`flex items-center px-6 py-3 rounded-lg transition-colors ${
                emergencyMessage.trim()
                  ? emergencyType === 'emergency'
                    ? 'bg-red-600 hover:bg-red-700'
                    : 'bg-orange-600 hover:bg-orange-700'
                  : 'bg-slate-600 cursor-not-allowed'
              } text-white`}
            >
              {emergencyType === 'emergency' ? (
                <AlertTriangle className="w-5 h-5 ml-2" />
              ) : (
                <Megaphone className="w-5 h-5 ml-2" />
              )}
              إرسال الإعلان العاجل
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminPanel;