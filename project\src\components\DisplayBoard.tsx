import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Calendar, Clock, Book, MapPin, Phone } from 'lucide-react';
import { useLocalStorage } from '../hooks/useLocalStorage';
import { Program, Mosque } from '../types';

const DisplayBoard: React.FC = () => {
  const { mosqueSlug } = useParams<{ mosqueSlug: string }>();
  const [currentTime, setCurrentTime] = useState(new Date());

  // البحث عن المسجد بناءً على الـ slug
  const [mosques] = useLocalStorage<Mosque[]>('mosques', []);
  const currentMosque = mosques.find(m => m.id === mosqueSlug);
  const mosqueId = mosqueSlug || 'default';

  // استخدام نفس البيانات المحفوظة في localStorage مع معرف المسجد
  const [mosqueData] = useLocalStorage<Mosque>(`mosque-${mosqueId}`,
    currentMosque || {
      id: mosqueId,
      name: 'مسجد النور المبارك',
      capacity: 500,
      mushafs: 150,
      worshippers: 320,
      area: 800,
      rows: 25,
      createdAt: new Date().toISOString()
    }
  );

  const [programs] = useLocalStorage<Program[]>(`programs-${mosqueId}`, []);

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('ar-SA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div 
      className="p-6 min-h-screen relative"
      style={{
        background: mosqueData.backgroundImage 
          ? `linear-gradient(rgba(15, 23, 42, 0.8), rgba(15, 23, 42, 0.9)), url(${mosqueData.backgroundImage})`
          : 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #065f46 100%)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-emerald-600/90 to-emerald-700/90 backdrop-blur-sm rounded-xl p-6 mb-6 shadow-2xl border border-emerald-500/20">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse">
            {mosqueData.logo && (
              <img 
                src={mosqueData.logo} 
                alt="شعار المسجد" 
                className="w-16 h-16 rounded-full object-cover border-2 border-white/20"
              />
            )}
            <div className="text-center flex-1">
              <h1 className="text-4xl font-bold text-white mb-2">{mosqueData.name}</h1>
              {mosqueData.address && (
                <p className="text-emerald-100 flex items-center justify-center">
                  <MapPin className="w-4 h-4 ml-2" />
                  {mosqueData.address}
                </p>
              )}
              {mosqueData.phone && (
                <p className="text-emerald-100 flex items-center justify-center mt-1">
                  <Phone className="w-4 h-4 ml-2" />
                  {mosqueData.phone}
                </p>
              )}
            </div>
          </div>
          <div className="text-left">
            <div className="flex items-center text-emerald-100 mb-2">
              <Clock className="w-5 h-5 ml-2" />
              <span className="text-2xl font-bold">{formatTime(currentTime)}</span>
            </div>
            <div className="flex items-center text-emerald-100">
              <Calendar className="w-5 h-5 ml-2" />
              <span className="text-lg">{formatDate(currentTime)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Emergency Banner */}
      <div className="bg-red-600/90 backdrop-blur-sm text-white p-3 rounded-lg mb-6 animate-pulse border border-red-500/30">
        <div className="flex items-center justify-center">
          <span className="bg-red-800 text-white px-2 py-1 rounded text-sm font-bold ml-3">
            شريط إعلامي
          </span>
          <div className="text-center font-medium">
            تنبيه: سيتم تغيير موعد درس التفسير إلى يوم الخميس بدلاً من الأربعاء
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="grid grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
        <div className="bg-emerald-700/80 backdrop-blur-sm rounded-lg p-4 text-center border border-emerald-600/30">
          <h3 className="text-emerald-200 text-sm mb-1">إحصائيات المساجد</h3>
          <div className="text-white font-bold text-xl">1</div>
        </div>
        <div className="bg-emerald-700/80 backdrop-blur-sm rounded-lg p-4 text-center border border-emerald-600/30">
          <h3 className="text-emerald-200 text-sm mb-1">عدد الصفوف</h3>
          <div className="text-white font-bold text-xl">{mosqueData.rows}</div>
        </div>
        <div className="bg-emerald-700/80 backdrop-blur-sm rounded-lg p-4 text-center border border-emerald-600/30">
          <h3 className="text-emerald-200 text-sm mb-1">عدد المصاحف</h3>
          <div className="text-white font-bold text-xl">{mosqueData.mushafs}</div>
        </div>
        <div className="bg-emerald-700/80 backdrop-blur-sm rounded-lg p-4 text-center border border-emerald-600/30">
          <h3 className="text-emerald-200 text-sm mb-1">عدد المأمومين</h3>
          <div className="text-white font-bold text-xl">{mosqueData.worshippers}</div>
        </div>
        <div className="bg-emerald-700/80 backdrop-blur-sm rounded-lg p-4 text-center border border-emerald-600/30">
          <h3 className="text-emerald-200 text-sm mb-1">مساحة المسجد</h3>
          <div className="text-white font-bold text-xl">{mosqueData.area}</div>
        </div>
        <div className="bg-emerald-700/80 backdrop-blur-sm rounded-lg p-4 text-center border border-emerald-600/30">
          <h3 className="text-emerald-200 text-sm mb-1">طاقة المصلين</h3>
          <div className="text-white font-bold text-xl">{mosqueData.capacity}</div>
        </div>
      </div>

      {/* Programs Schedule */}
      <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-2xl border border-white/20">
        <h2 className="text-2xl font-bold text-emerald-400 mb-4 text-center">
          البرامج الحالية ({programs.length})
        </h2>
        
        {programs.length === 0 ? (
          <div className="text-center text-slate-300 py-8">
            <Book className="w-16 h-16 mx-auto mb-4 text-slate-400" />
            <p className="text-lg">لا توجد برامج مضافة حالياً</p>
            <p className="text-sm mt-2">يمكنك إضافة برامج جديدة من لوحة التحكم</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="bg-emerald-600/80 text-white">
                  <th className="p-3 text-right">النوع</th>
                  <th className="p-3 text-right">التاريخ الهجري</th>
                  <th className="p-3 text-right">التاريخ الميلادي</th>
                  <th className="p-3 text-right">البرنامج</th>
                  <th className="p-3 text-right">المستوى</th>
                  <th className="p-3 text-right">الحالة</th>
                  <th className="p-3 text-right">وقت البداية</th>
                  <th className="p-3 text-right">وقت النهاية</th>
                  <th className="p-3 text-right">عدد الحضور</th>
                  <th className="p-3 text-right">المحاضر</th>
                  <th className="p-3 text-right">ملاحظات</th>
                </tr>
              </thead>
              <tbody>
                {programs.map((program, index) => (
                  <tr 
                    key={program.id} 
                    className={`${
                      index % 2 === 0 ? 'bg-slate-700/30' : 'bg-slate-600/30'
                    } border-b border-slate-600/50 hover:bg-emerald-600/20 transition-colors`}
                  >
                    <td className="p-3 text-slate-300">{program.type}</td>
                    <td className="p-3 text-slate-300">{program.hijriDate}</td>
                    <td className="p-3 text-slate-300">{program.gregorianDate}</td>
                    <td className="p-3 text-white font-medium">{program.name}</td>
                    <td className="p-3 text-slate-300">{program.level}</td>
                    <td className="p-3">
                      <span className="bg-green-600 text-white px-2 py-1 rounded text-xs">
                        {program.status}
                      </span>
                    </td>
                    <td className="p-3 text-emerald-400 font-medium">{program.timeFrom}</td>
                    <td className="p-3 text-emerald-400 font-medium">{program.timeTo}</td>
                    <td className="p-3 text-white font-bold">{program.attendance}</td>
                    <td className="p-3 text-blue-400 font-medium">{program.lecturer}</td>
                    <td className="p-3 text-slate-300 text-xs">{program.notes}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="mt-6 text-center text-slate-300">
        <p>تحديث تلقائي كل 30 ثانية • آخر تحديث: {formatTime(currentTime)}</p>
      </div>
    </div>
  );
};

export default DisplayBoard;