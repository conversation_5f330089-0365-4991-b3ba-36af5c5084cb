import { createClient } from '@supabase/supabase-js';

// قراءة المفاتيح من متغيرات البيئة
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// أنواع البيانات لـ TypeScript
export interface Database {
  public: {
    Tables: {
      mosques: {
        Row: {
          id: string;
          name: string;
          address: string | null;
          phone: string | null;
          capacity: number;
          mushafs: number;
          worshippers: number;
          area: number;
          rows: number;
          logo_url: string | null;
          background_image_url: string | null;
          created_at: string;
        };
        Insert: {
          id: string;
          name: string;
          address?: string | null;
          phone?: string | null;
          capacity?: number;
          mushafs?: number;
          worshippers?: number;
          area?: number;
          rows?: number;
          logo_url?: string | null;
          background_image_url?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          address?: string | null;
          phone?: string | null;
          capacity?: number;
          mushafs?: number;
          worshippers?: number;
          area?: number;
          rows?: number;
          logo_url?: string | null;
          background_image_url?: string | null;
          created_at?: string;
        };
      };
      programs: {
        Row: {
          id: string;
          mosque_id: string;
          name: string;
          lecturer: string;
          level: string;
          attendance: number;
          time_from: string;
          time_to: string;
          status: string;
          type: string;
          notes: string;
          hijri_date: string;
          gregorian_date: string;
          days: string[];
          created_at: string;
        };
        Insert: {
          id?: string;
          mosque_id: string;
          name: string;
          lecturer?: string;
          level?: string;
          attendance?: number;
          time_from?: string;
          time_to?: string;
          status?: string;
          type?: string;
          notes?: string;
          hijri_date?: string;
          gregorian_date?: string;
          days?: string[];
          created_at?: string;
        };
        Update: {
          id?: string;
          mosque_id?: string;
          name?: string;
          lecturer?: string;
          level?: string;
          attendance?: number;
          time_from?: string;
          time_to?: string;
          status?: string;
          type?: string;
          notes?: string;
          hijri_date?: string;
          gregorian_date?: string;
          days?: string[];
          created_at?: string;
        };
      };
      users: {
        Row: {
          id: string;
          username: string;
          mosque_id: string;
          role: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          username: string;
          mosque_id: string;
          role?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          username?: string;
          mosque_id?: string;
          role?: string;
          created_at?: string;
        };
      };
    };
  };
}
