import { useState, useEffect } from 'react';
import { useLocalStorage } from './useLocalStorage';
import { User, Mosque } from '../types';

export function useAuth() {
  const [currentUser, setCurrentUser] = useLocalStorage<User | null>('currentUser', null);
  const [users] = useLocalStorage<User[]>('users', [
    {
      id: '1',
      username: 'admin',
      mosqueId: 'mosque-1',
      role: 'admin',
      createdAt: new Date().toISOString()
    }
  ]);
  
  const [mosques] = useLocalStorage<Mosque[]>('mosques', [
    {
      id: 'mosque-1',
      name: 'مسجد النور المبارك',
      capacity: 500,
      mushafs: 150,
      worshippers: 320,
      area: 800,
      rows: 25,
      address: 'الرياض، المملكة العربية السعودية',
      phone: '+966 11 123 4567',
      createdAt: new Date().toISOString()
    }
  ]);

  const login = (username: string, password: string): boolean => {
    // Simple authentication - in production, use proper authentication
    const user = users.find(u => u.username === username);
    if (user && password === 'admin123') {
      setCurrentUser(user);
      return true;
    }
    return false;
  };

  const logout = () => {
    setCurrentUser(null);
  };

  const getCurrentMosque = (): Mosque | null => {
    if (!currentUser) return null;
    return mosques.find(m => m.id === currentUser.mosqueId) || null;
  };

  return {
    currentUser,
    isAuthenticated: !!currentUser,
    login,
    logout,
    getCurrentMosque
  };
}