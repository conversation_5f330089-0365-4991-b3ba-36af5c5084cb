
import { useLocalStorage } from './useLocalStorage';
import { User, Mosque } from '../types';

export function useAuth(mosqueId?: string) {
  const [currentUser, setCurrentUser] = useLocalStorage<User | null>('currentUser', null);
  const [mosques] = useLocalStorage<Mosque[]>('mosques', []);

  const login = (username: string, password: string): boolean => {
    // Simple authentication - in production, use proper authentication
    // For now, accept admin/admin123 for any mosque
    if (username === 'admin' && password === 'admin123') {
      const user: User = {
        id: `admin-${mosqueId || 'default'}`,
        username: 'admin',
        mosqueId: mosqueId || 'default',
        role: 'admin',
        createdAt: new Date().toISOString()
      };
      setCurrentUser(user);
      return true;
    }
    return false;
  };

  const logout = () => {
    setCurrentUser(null);
  };

  const getCurrentMosque = (): Mosque | null => {
    if (!currentUser) return null;
    return mosques.find(m => m.id === currentUser.mosqueId) || null;
  };

  return {
    currentUser,
    isAuthenticated: !!currentUser,
    login,
    logout,
    getCurrentMosque
  };
}