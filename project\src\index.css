@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  font-family: 'Cairo', sans-serif;
}

body {
  direction: rtl;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from { 
    opacity: 0;
    transform: translateY(30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideInUp {
  animation: slideInUp 0.4s ease-out;
}

/* Custom scrollbar for tables */
::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #059669;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #047857;
}

/* Table responsiveness */
.table-container {
  min-width: 100%;
  overflow-x: auto;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .bg-slate-900,
  .bg-slate-800,
  .bg-slate-700 {
    background: white !important;
    color: black !important;
  }
}