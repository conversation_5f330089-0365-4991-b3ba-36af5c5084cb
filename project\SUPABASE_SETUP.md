# 🚀 دليل إعداد Supabase لنظام إعلانات المساجد

## الخطوة 1: إنشاء حساب Supabase

1. اذهب إلى [supabase.com](https://supabase.com)
2. انقر على "Start your project"
3. سجل دخول باستخدام GitHub أو Google
4. انقر على "New Project"

## الخطوة 2: إعداد المشروع

1. **اختر Organization** (أو أنشئ واحدة جديدة)
2. **اسم المشروع**: `mosque-announcements`
3. **كلمة مرور قاعدة البيانات**: اختر كلمة مرور قوية واحفظها
4. **المنطقة**: اختر أقرب منطقة (مثل `Southeast Asia` أو `Europe West`)
5. انقر على "Create new project"

## الخطوة 3: الحصول على مفاتيح API

بعد إنشاء المشروع:

1. اذهب إلى **Settings** > **API**
2. انسخ القيم التالية:
   - **Project URL**: `https://your-project-id.supabase.co`
   - **anon public key**: مفتاح طويل يبدأ بـ `eyJ...`

## الخطوة 4: تحديث ملف .env

افتح ملف `.env` في مجلد المشروع وحدث القيم:

```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## الخطوة 5: إنشاء الجداول

1. اذهب إلى **SQL Editor** في لوحة تحكم Supabase
2. انسخ محتوى ملف `supabase-setup.sql`
3. الصق الكود في المحرر
4. انقر على "Run" لتنفيذ الكود

## الخطوة 6: التحقق من الإعداد

بعد تنفيذ SQL:

1. اذهب إلى **Table Editor**
2. يجب أن ترى 3 جداول:
   - `mosques` (المساجد)
   - `programs` (البرامج)
   - `users` (المستخدمين)

## الخطوة 7: تشغيل المشروع

```bash
npm run dev
```

## 🔧 إعدادات إضافية (اختيارية)

### تفعيل Storage للصور

1. اذهب إلى **Storage**
2. انقر على "Create a new bucket"
3. اسم الـ bucket: `mosque-images`
4. اجعله `Public bucket`
5. انقر على "Create bucket"

### إعداد Authentication (إذا أردت مصادقة متقدمة)

1. اذهب إلى **Authentication** > **Settings**
2. فعّل **Email confirmations** إذا أردت
3. أضف **Site URL**: `http://localhost:5173` للتطوير

## 🚨 ملاحظات مهمة

- **احفظ كلمة مرور قاعدة البيانات** في مكان آمن
- **لا تشارك مفاتيح API** في الكود العام
- **استخدم متغيرات البيئة** دائماً للمفاتيح
- **المشروع المجاني** يدعم حتى 500MB و 2GB bandwidth شهرياً

## 🔍 استكشاف الأخطاء

### خطأ في الاتصال
- تأكد من صحة URL و API Key
- تأكد من أن المشروع نشط في Supabase

### خطأ في الجداول
- تأكد من تنفيذ ملف SQL بالكامل
- تحقق من وجود الجداول في Table Editor

### خطأ في الصلاحيات
- تأكد من تفعيل RLS policies
- تحقق من إعدادات الأمان في Authentication

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع [وثائق Supabase](https://supabase.com/docs)
2. تحقق من [مجتمع Supabase](https://github.com/supabase/supabase/discussions)
3. أو اسأل المطور! 😊
