export interface User {
  id: string;
  username: string;
  mosqueId: string;
  role: 'admin' | 'manager';
  createdAt: string;
}

export interface Mosque {
  id: string;
  name: string;
  capacity: number;
  mushafs: number;
  worshippers: number;
  area: number;
  rows: number;
  logo?: string;
  backgroundImage?: string;
  address?: string;
  phone?: string;
  createdAt: string;
}

export interface Program {
  id: string;
  mosqueId: string;
  name: string;
  lecturer: string;
  level: string;
  attendance: number;
  timeFrom: string;
  timeTo: string;
  status: string;
  type: string;
  notes: string;
  hijriDate: string;
  gregorianDate: string;
  days: string[];
}

export interface EmergencyAlert {
  id: string;
  mosqueId: string;
  message: string;
  type: 'emergency' | 'announcement';
  isActive: boolean;
  createdAt: string;
  expiresAt?: string;
}