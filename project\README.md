# 🕌 نظام إعلانات المساجد الرقمي

نظام شامل لإدارة وعرض إعلانات المساجد مع دعم مساجد متعددة وقاعدة بيانات سحابية.

## ✨ المميزات

### 🎯 **للمساجد**
- **رابط مخصص لكل مسجد**: `yoursite.com/masjid-name`
- **لوحة تحكم منفصلة**: `yoursite.com/admin/masjid-name`
- **عرض عام بدون تسجيل دخول** للزوار
- **حماية بسيطة بكلمة مرور** للوحة التحكم

### 📊 **إدارة شاملة**
- **بيانات المسجد**: الاسم، العنوان، الطاقة، إلخ
- **إدارة البرامج**: إضافة وتعديل وحذف البرامج
- **الإعلانات العاجلة**: إرسال إعلانات فورية
- **رفع الصور**: شعارات وخلفيات المساجد

### 🔒 **قاعدة بيانات آمنة**
- **Supabase PostgreSQL** - قاعدة بيانات قوية ومجانية
- **مزامنة فورية** - لا فقدان للبيانات
- **نسخ احتياطي تلقائي** - أمان عالي
- **Row Level Security** - حماية متقدمة

## 🚀 التشغيل السريع

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd mosque-announcements
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. إعداد Supabase
اتبع التعليمات في ملف [SUPABASE_SETUP.md](./SUPABASE_SETUP.md)

### 4. تحديث متغيرات البيئة
```bash
# انسخ ملف .env وحدث القيم
cp .env.example .env
```

أضف مفاتيح Supabase في `.env`:
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### 5. تشغيل المشروع
```bash
npm run dev
```

## 📁 هيكل المشروع

```
project/
├── src/
│   ├── components/          # المكونات المشتركة
│   │   ├── AdminPanel.tsx   # لوحة التحكم
│   │   ├── DisplayBoard.tsx # لوحة العرض
│   │   ├── LoginForm.tsx    # نموذج تسجيل الدخول
│   │   └── ...
│   ├── pages/               # الصفحات الرئيسية
│   │   ├── HomePage.tsx     # الصفحة الرئيسية
│   │   ├── PublicDisplay.tsx # العرض العام
│   │   └── AdminPage.tsx    # صفحة الإدارة
│   ├── hooks/               # React Hooks
│   │   ├── useMosques.ts    # إدارة المساجد
│   │   ├── usePrograms.ts   # إدارة البرامج
│   │   └── useAuth.ts       # المصادقة
│   ├── lib/                 # المكتبات
│   │   └── supabase.ts      # إعداد Supabase
│   └── types/               # أنواع TypeScript
├── supabase-setup.sql       # إعداد قاعدة البيانات
├── SUPABASE_SETUP.md        # دليل إعداد Supabase
└── README.md                # هذا الملف
```

## 🌐 الروابط والاستخدام

### للزوار (عرض عام)
```
https://yoursite.com/masjid-alnour     # مسجد النور
https://yoursite.com/masjid-alrahma    # مسجد الرحمة
```

### للإدارة (لوحة التحكم)
```
https://yoursite.com/admin/masjid-alnour   # إدارة مسجد النور
https://yoursite.com/admin/masjid-alrahma  # إدارة مسجد الرحمة
```

**بيانات تسجيل الدخول:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## 🛠️ التقنيات المستخدمة

- **React 18** - مكتبة واجهة المستخدم
- **TypeScript** - لغة البرمجة
- **Vite** - أداة البناء
- **Tailwind CSS** - تصميم الواجهات
- **React Router** - التنقل بين الصفحات
- **Supabase** - قاعدة البيانات والخلفية
- **Lucide React** - الأيقونات

## 📱 التصميم المتجاوب

- ✅ **الهواتف الذكية** - تصميم محسن للشاشات الصغيرة
- ✅ **الأجهزة اللوحية** - واجهة مناسبة للشاشات المتوسطة  
- ✅ **أجهزة الكمبيوتر** - استغلال كامل للشاشات الكبيرة
- ✅ **الشاشات الكبيرة** - مناسب لشاشات العرض في المساجد

## 🔧 التطوير

### إضافة مسجد جديد
1. اذهب إلى الصفحة الرئيسية
2. انقر على "إضافة مسجد جديد"
3. أدخل البيانات المطلوبة
4. سيتم إنشاء الروابط تلقائياً

### تخصيص التصميم
- عدل ملفات CSS في `src/index.css`
- استخدم فئات Tailwind للتخصيص السريع
- أضف ألوان مخصصة في `tailwind.config.js`

### إضافة مميزات جديدة
- أنشئ مكونات جديدة في `src/components/`
- أضف صفحات جديدة في `src/pages/`
- استخدم hooks مخصصة في `src/hooks/`

## 🚀 النشر

### Vercel (مجاني)
```bash
npm install -g vercel
vercel
```

### Netlify (مجاني)
```bash
npm run build
# ارفع مجلد dist إلى Netlify
```

### خادم مخصص
```bash
npm run build
# انسخ مجلد dist إلى خادمك
```

## 📞 الدعم والمساعدة

- 📧 **البريد الإلكتروني**: [<EMAIL>]
- 💬 **المناقشات**: [GitHub Discussions]
- 🐛 **الأخطاء**: [GitHub Issues]
- 📖 **الوثائق**: [Documentation Link]

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**صُنع بـ ❤️ لخدمة المساجد والمجتمع المسلم**
