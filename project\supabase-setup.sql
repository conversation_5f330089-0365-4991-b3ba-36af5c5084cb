-- إن<PERSON>اء جدول المساجد
CREATE TABLE IF NOT EXISTS mosques (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    address TEXT,
    phone TEXT,
    capacity INTEGER DEFAULT 500,
    mushafs INTEGER DEFAULT 150,
    worshippers INTEGER DEFAULT 320,
    area INTEGER DEFAULT 800,
    rows INTEGER DEFAULT 25,
    logo_url TEXT,
    background_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول البرامج
CREATE TABLE IF NOT EXISTS programs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    mosque_id TEXT NOT NULL REFERENCES mosques(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    lecturer TEXT DEFAULT '',
    level TEXT DEFAULT 'مبتدئ',
    attendance INTEGER DEFAULT 0,
    time_from TEXT DEFAULT '09:00',
    time_to TEXT DEFAULT '10:00',
    status TEXT DEFAULT 'نشط',
    type TEXT DEFAULT 'عام',
    notes TEXT DEFAULT '',
    hijri_date TEXT DEFAULT '',
    gregorian_date TEXT DEFAULT '',
    days TEXT[] DEFAULT ARRAY['الأحد'],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username TEXT NOT NULL,
    mosque_id TEXT NOT NULL REFERENCES mosques(id) ON DELETE CASCADE,
    role TEXT DEFAULT 'admin',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_programs_mosque_id ON programs(mosque_id);
CREATE INDEX IF NOT EXISTS idx_users_mosque_id ON users(mosque_id);
CREATE INDEX IF NOT EXISTS idx_mosques_created_at ON mosques(created_at);
CREATE INDEX IF NOT EXISTS idx_programs_created_at ON programs(created_at);

-- إعداد Row Level Security (RLS)
ALTER TABLE mosques ENABLE ROW LEVEL SECURITY;
ALTER TABLE programs ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للمساجد (قراءة عامة، كتابة محدودة)
CREATE POLICY "Allow public read access to mosques" ON mosques
    FOR SELECT USING (true);

CREATE POLICY "Allow insert for mosques" ON mosques
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow update for mosques" ON mosques
    FOR UPDATE USING (true);

-- سياسات الأمان للبرامج (قراءة عامة، كتابة محدودة)
CREATE POLICY "Allow public read access to programs" ON programs
    FOR SELECT USING (true);

CREATE POLICY "Allow insert for programs" ON programs
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow update for programs" ON programs
    FOR UPDATE USING (true);

CREATE POLICY "Allow delete for programs" ON programs
    FOR DELETE USING (true);

-- سياسات الأمان للمستخدمين (محدودة)
CREATE POLICY "Allow read access to users" ON users
    FOR SELECT USING (true);

CREATE POLICY "Allow insert for users" ON users
    FOR INSERT WITH CHECK (true);

-- إدراج بيانات تجريبية
INSERT INTO mosques (id, name, address, phone, capacity, mushafs, worshippers, area, rows) VALUES
('masjid-alnour', 'مسجد النور المبارك', 'الرياض، المملكة العربية السعودية', '+966 11 123 4567', 500, 150, 320, 800, 25),
('masjid-alrahma', 'مسجد الرحمة', 'جدة، المملكة العربية السعودية', '+966 12 234 5678', 400, 120, 280, 600, 20),
('masjid-altaqwa', 'مسجد التقوى', 'الدمام، المملكة العربية السعودية', '+966 13 345 6789', 300, 100, 200, 500, 15)
ON CONFLICT (id) DO NOTHING;

-- إدراج برامج تجريبية
INSERT INTO programs (mosque_id, name, lecturer, level, attendance, time_from, time_to, status, type, notes, hijri_date, gregorian_date, days) VALUES
('masjid-alnour', 'تفسير القرآن الكريم', 'الشيخ أحمد محمد', 'متوسط', 45, '20:00', '21:30', 'نشط', 'تعليمي', 'درس أسبوعي في تفسير القرآن الكريم', '1445/06/15', '2024/01/15', ARRAY['الثلاثاء']),
('masjid-alnour', 'دروس الفقه', 'الشيخ محمد علي', 'مبتدئ', 30, '19:00', '20:00', 'نشط', 'تعليمي', 'أساسيات الفقه الإسلامي', '1445/06/16', '2024/01/16', ARRAY['الأربعاء']),
('masjid-alrahma', 'حلقة تحفيظ القرآن', 'الأستاذ عبدالله سالم', 'جميع المستويات', 25, '17:00', '18:30', 'نشط', 'تحفيظ', 'حلقة تحفيظ للأطفال والكبار', '1445/06/17', '2024/01/17', ARRAY['السبت', 'الاثنين'])
ON CONFLICT DO NOTHING;

-- إدراج مستخدمين تجريبيين
INSERT INTO users (username, mosque_id, role) VALUES
('admin', 'masjid-alnour', 'admin'),
('admin', 'masjid-alrahma', 'admin'),
('admin', 'masjid-altaqwa', 'admin')
ON CONFLICT DO NOTHING;
