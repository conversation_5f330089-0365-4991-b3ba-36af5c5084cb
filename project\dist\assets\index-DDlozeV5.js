(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();var Ae=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Nf(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}),n}var wc={exports:{}},mi={},xc={exports:{}},A={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qr=Symbol.for("react.element"),Cf=Symbol.for("react.portal"),Pf=Symbol.for("react.fragment"),Tf=Symbol.for("react.strict_mode"),Rf=Symbol.for("react.profiler"),Of=Symbol.for("react.provider"),Lf=Symbol.for("react.context"),$f=Symbol.for("react.forward_ref"),If=Symbol.for("react.suspense"),Af=Symbol.for("react.memo"),Df=Symbol.for("react.lazy"),za=Symbol.iterator;function Mf(e){return e===null||typeof e!="object"?null:(e=za&&e[za]||e["@@iterator"],typeof e=="function"?e:null)}var _c={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},kc=Object.assign,Sc={};function Wn(e,t,n){this.props=e,this.context=t,this.refs=Sc,this.updater=n||_c}Wn.prototype.isReactComponent={};Wn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Wn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function jc(){}jc.prototype=Wn.prototype;function Rl(e,t,n){this.props=e,this.context=t,this.refs=Sc,this.updater=n||_c}var Ol=Rl.prototype=new jc;Ol.constructor=Rl;kc(Ol,Wn.prototype);Ol.isPureReactComponent=!0;var Fa=Array.isArray,Ec=Object.prototype.hasOwnProperty,Ll={current:null},bc={key:!0,ref:!0,__self:!0,__source:!0};function Nc(e,t,n){var r,s={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)Ec.call(t,r)&&!bc.hasOwnProperty(r)&&(s[r]=t[r]);var l=arguments.length-2;if(l===1)s.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];s.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)s[r]===void 0&&(s[r]=l[r]);return{$$typeof:qr,type:e,key:i,ref:o,props:s,_owner:Ll.current}}function Uf(e,t){return{$$typeof:qr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function $l(e){return typeof e=="object"&&e!==null&&e.$$typeof===qr}function zf(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ba=/\/+/g;function Wi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?zf(""+e.key):t.toString(36)}function bs(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case qr:case Cf:o=!0}}if(o)return o=e,s=s(o),e=r===""?"."+Wi(o,0):r,Fa(s)?(n="",e!=null&&(n=e.replace(Ba,"$&/")+"/"),bs(s,t,n,"",function(u){return u})):s!=null&&($l(s)&&(s=Uf(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(Ba,"$&/")+"/")+e)),t.push(s)),1;if(o=0,r=r===""?".":r+":",Fa(e))for(var l=0;l<e.length;l++){i=e[l];var a=r+Wi(i,l);o+=bs(i,t,n,a,s)}else if(a=Mf(e),typeof a=="function")for(e=a.call(e),l=0;!(i=e.next()).done;)i=i.value,a=r+Wi(i,l++),o+=bs(i,t,n,a,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function rs(e,t,n){if(e==null)return e;var r=[],s=0;return bs(e,r,"","",function(i){return t.call(n,i,s++)}),r}function Ff(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ve={current:null},Ns={transition:null},Bf={ReactCurrentDispatcher:ve,ReactCurrentBatchConfig:Ns,ReactCurrentOwner:Ll};function Cc(){throw Error("act(...) is not supported in production builds of React.")}A.Children={map:rs,forEach:function(e,t,n){rs(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return rs(e,function(){t++}),t},toArray:function(e){return rs(e,function(t){return t})||[]},only:function(e){if(!$l(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};A.Component=Wn;A.Fragment=Pf;A.Profiler=Rf;A.PureComponent=Rl;A.StrictMode=Tf;A.Suspense=If;A.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Bf;A.act=Cc;A.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=kc({},e.props),s=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=Ll.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)Ec.call(t,a)&&!bc.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:qr,type:e.type,key:s,ref:i,props:r,_owner:o}};A.createContext=function(e){return e={$$typeof:Lf,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Of,_context:e},e.Consumer=e};A.createElement=Nc;A.createFactory=function(e){var t=Nc.bind(null,e);return t.type=e,t};A.createRef=function(){return{current:null}};A.forwardRef=function(e){return{$$typeof:$f,render:e}};A.isValidElement=$l;A.lazy=function(e){return{$$typeof:Df,_payload:{_status:-1,_result:e},_init:Ff}};A.memo=function(e,t){return{$$typeof:Af,type:e,compare:t===void 0?null:t}};A.startTransition=function(e){var t=Ns.transition;Ns.transition={};try{e()}finally{Ns.transition=t}};A.unstable_act=Cc;A.useCallback=function(e,t){return ve.current.useCallback(e,t)};A.useContext=function(e){return ve.current.useContext(e)};A.useDebugValue=function(){};A.useDeferredValue=function(e){return ve.current.useDeferredValue(e)};A.useEffect=function(e,t){return ve.current.useEffect(e,t)};A.useId=function(){return ve.current.useId()};A.useImperativeHandle=function(e,t,n){return ve.current.useImperativeHandle(e,t,n)};A.useInsertionEffect=function(e,t){return ve.current.useInsertionEffect(e,t)};A.useLayoutEffect=function(e,t){return ve.current.useLayoutEffect(e,t)};A.useMemo=function(e,t){return ve.current.useMemo(e,t)};A.useReducer=function(e,t,n){return ve.current.useReducer(e,t,n)};A.useRef=function(e){return ve.current.useRef(e)};A.useState=function(e){return ve.current.useState(e)};A.useSyncExternalStore=function(e,t,n){return ve.current.useSyncExternalStore(e,t,n)};A.useTransition=function(){return ve.current.useTransition()};A.version="18.3.1";xc.exports=A;var x=xc.exports;/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hf=x,Wf=Symbol.for("react.element"),qf=Symbol.for("react.fragment"),Vf=Object.prototype.hasOwnProperty,Kf=Hf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Qf={key:!0,ref:!0,__self:!0,__source:!0};function Pc(e,t,n){var r,s={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)Vf.call(t,r)&&!Qf.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:Wf,type:e,key:i,ref:o,props:s,_owner:Kf.current}}mi.Fragment=qf;mi.jsx=Pc;mi.jsxs=Pc;wc.exports=mi;var c=wc.exports,Tc={exports:{}},Te={},Rc={exports:{}},Oc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,L){var $=P.length;P.push(L);e:for(;0<$;){var Y=$-1>>>1,ne=P[Y];if(0<s(ne,L))P[Y]=L,P[$]=ne,$=Y;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var L=P[0],$=P.pop();if($!==L){P[0]=$;e:for(var Y=0,ne=P.length,ts=ne>>>1;Y<ts;){var Ht=2*(Y+1)-1,Hi=P[Ht],Wt=Ht+1,ns=P[Wt];if(0>s(Hi,$))Wt<ne&&0>s(ns,Hi)?(P[Y]=ns,P[Wt]=$,Y=Wt):(P[Y]=Hi,P[Ht]=$,Y=Ht);else if(Wt<ne&&0>s(ns,$))P[Y]=ns,P[Wt]=$,Y=Wt;else break e}}return L}function s(P,L){var $=P.sortIndex-L.sortIndex;return $!==0?$:P.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,l=o.now();e.unstable_now=function(){return o.now()-l}}var a=[],u=[],d=1,h=null,f=3,m=!1,y=!1,w=!1,k=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(P){for(var L=n(u);L!==null;){if(L.callback===null)r(u);else if(L.startTime<=P)r(u),L.sortIndex=L.expirationTime,t(a,L);else break;L=n(u)}}function _(P){if(w=!1,v(P),!y)if(n(a)!==null)y=!0,Fi(S);else{var L=n(u);L!==null&&Bi(_,L.startTime-P)}}function S(P,L){y=!1,w&&(w=!1,g(T),T=-1),m=!0;var $=f;try{for(v(L),h=n(a);h!==null&&(!(h.expirationTime>L)||P&&!N());){var Y=h.callback;if(typeof Y=="function"){h.callback=null,f=h.priorityLevel;var ne=Y(h.expirationTime<=L);L=e.unstable_now(),typeof ne=="function"?h.callback=ne:h===n(a)&&r(a),v(L)}else r(a);h=n(a)}if(h!==null)var ts=!0;else{var Ht=n(u);Ht!==null&&Bi(_,Ht.startTime-L),ts=!1}return ts}finally{h=null,f=$,m=!1}}var E=!1,b=null,T=-1,D=5,R=-1;function N(){return!(e.unstable_now()-R<D)}function fe(){if(b!==null){var P=e.unstable_now();R=P;var L=!0;try{L=b(!0,P)}finally{L?Xn():(E=!1,b=null)}}else E=!1}var Xn;if(typeof p=="function")Xn=function(){p(fe)};else if(typeof MessageChannel<"u"){var Ua=new MessageChannel,bf=Ua.port2;Ua.port1.onmessage=fe,Xn=function(){bf.postMessage(null)}}else Xn=function(){k(fe,0)};function Fi(P){b=P,E||(E=!0,Xn())}function Bi(P,L){T=k(function(){P(e.unstable_now())},L)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){y||m||(y=!0,Fi(S))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):D=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(P){switch(f){case 1:case 2:case 3:var L=3;break;default:L=f}var $=f;f=L;try{return P()}finally{f=$}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,L){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var $=f;f=P;try{return L()}finally{f=$}},e.unstable_scheduleCallback=function(P,L,$){var Y=e.unstable_now();switch(typeof $=="object"&&$!==null?($=$.delay,$=typeof $=="number"&&0<$?Y+$:Y):$=Y,P){case 1:var ne=-1;break;case 2:ne=250;break;case 5:ne=**********;break;case 4:ne=1e4;break;default:ne=5e3}return ne=$+ne,P={id:d++,callback:L,priorityLevel:P,startTime:$,expirationTime:ne,sortIndex:-1},$>Y?(P.sortIndex=$,t(u,P),n(a)===null&&P===n(u)&&(w?(g(T),T=-1):w=!0,Bi(_,$-Y))):(P.sortIndex=ne,t(a,P),y||m||(y=!0,Fi(S))),P},e.unstable_shouldYield=N,e.unstable_wrapCallback=function(P){var L=f;return function(){var $=f;f=L;try{return P.apply(this,arguments)}finally{f=$}}}})(Oc);Rc.exports=Oc;var Gf=Rc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jf=x,Pe=Gf;function j(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Lc=new Set,br={};function ln(e,t){In(e,t),In(e+"Capture",t)}function In(e,t){for(br[e]=t,e=0;e<t.length;e++)Lc.add(t[e])}var dt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Eo=Object.prototype.hasOwnProperty,Yf=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ha={},Wa={};function Xf(e){return Eo.call(Wa,e)?!0:Eo.call(Ha,e)?!1:Yf.test(e)?Wa[e]=!0:(Ha[e]=!0,!1)}function Zf(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ep(e,t,n,r){if(t===null||typeof t>"u"||Zf(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ye(e,t,n,r,s,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var ae={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ae[e]=new ye(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ae[t]=new ye(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ae[e]=new ye(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ae[e]=new ye(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ae[e]=new ye(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ae[e]=new ye(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ae[e]=new ye(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ae[e]=new ye(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ae[e]=new ye(e,5,!1,e.toLowerCase(),null,!1,!1)});var Il=/[\-:]([a-z])/g;function Al(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Il,Al);ae[t]=new ye(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Il,Al);ae[t]=new ye(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Il,Al);ae[t]=new ye(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ae[e]=new ye(e,1,!1,e.toLowerCase(),null,!1,!1)});ae.xlinkHref=new ye("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ae[e]=new ye(e,1,!1,e.toLowerCase(),null,!0,!0)});function Dl(e,t,n,r){var s=ae.hasOwnProperty(t)?ae[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ep(t,n,s,r)&&(n=null),r||s===null?Xf(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var gt=Jf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ss=Symbol.for("react.element"),gn=Symbol.for("react.portal"),vn=Symbol.for("react.fragment"),Ml=Symbol.for("react.strict_mode"),bo=Symbol.for("react.profiler"),$c=Symbol.for("react.provider"),Ic=Symbol.for("react.context"),Ul=Symbol.for("react.forward_ref"),No=Symbol.for("react.suspense"),Co=Symbol.for("react.suspense_list"),zl=Symbol.for("react.memo"),wt=Symbol.for("react.lazy"),Ac=Symbol.for("react.offscreen"),qa=Symbol.iterator;function Zn(e){return e===null||typeof e!="object"?null:(e=qa&&e[qa]||e["@@iterator"],typeof e=="function"?e:null)}var Q=Object.assign,qi;function ur(e){if(qi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);qi=t&&t[1]||""}return`
`+qi+e}var Vi=!1;function Ki(e,t){if(!e||Vi)return"";Vi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),i=r.stack.split(`
`),o=s.length-1,l=i.length-1;1<=o&&0<=l&&s[o]!==i[l];)l--;for(;1<=o&&0<=l;o--,l--)if(s[o]!==i[l]){if(o!==1||l!==1)do if(o--,l--,0>l||s[o]!==i[l]){var a=`
`+s[o].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=o&&0<=l);break}}}finally{Vi=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ur(e):""}function tp(e){switch(e.tag){case 5:return ur(e.type);case 16:return ur("Lazy");case 13:return ur("Suspense");case 19:return ur("SuspenseList");case 0:case 2:case 15:return e=Ki(e.type,!1),e;case 11:return e=Ki(e.type.render,!1),e;case 1:return e=Ki(e.type,!0),e;default:return""}}function Po(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case vn:return"Fragment";case gn:return"Portal";case bo:return"Profiler";case Ml:return"StrictMode";case No:return"Suspense";case Co:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ic:return(e.displayName||"Context")+".Consumer";case $c:return(e._context.displayName||"Context")+".Provider";case Ul:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case zl:return t=e.displayName||null,t!==null?t:Po(e.type)||"Memo";case wt:t=e._payload,e=e._init;try{return Po(e(t))}catch{}}return null}function np(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Po(t);case 8:return t===Ml?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function At(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Dc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function rp(e){var t=Dc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function is(e){e._valueTracker||(e._valueTracker=rp(e))}function Mc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Dc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Fs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function To(e,t){var n=t.checked;return Q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Va(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=At(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Uc(e,t){t=t.checked,t!=null&&Dl(e,"checked",t,!1)}function Ro(e,t){Uc(e,t);var n=At(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Oo(e,t.type,n):t.hasOwnProperty("defaultValue")&&Oo(e,t.type,At(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ka(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Oo(e,t,n){(t!=="number"||Fs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var cr=Array.isArray;function Cn(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+At(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Lo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(j(91));return Q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Qa(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(j(92));if(cr(n)){if(1<n.length)throw Error(j(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:At(n)}}function zc(e,t){var n=At(t.value),r=At(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ga(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Fc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function $o(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Fc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var os,Bc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(os=os||document.createElement("div"),os.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=os.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Nr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var pr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},sp=["Webkit","ms","Moz","O"];Object.keys(pr).forEach(function(e){sp.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pr[t]=pr[e]})});function Hc(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||pr.hasOwnProperty(e)&&pr[e]?(""+t).trim():t+"px"}function Wc(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Hc(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var ip=Q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Io(e,t){if(t){if(ip[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(j(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(j(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(j(61))}if(t.style!=null&&typeof t.style!="object")throw Error(j(62))}}function Ao(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Do=null;function Fl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Mo=null,Pn=null,Tn=null;function Ja(e){if(e=Qr(e)){if(typeof Mo!="function")throw Error(j(280));var t=e.stateNode;t&&(t=xi(t),Mo(e.stateNode,e.type,t))}}function qc(e){Pn?Tn?Tn.push(e):Tn=[e]:Pn=e}function Vc(){if(Pn){var e=Pn,t=Tn;if(Tn=Pn=null,Ja(e),t)for(e=0;e<t.length;e++)Ja(t[e])}}function Kc(e,t){return e(t)}function Qc(){}var Qi=!1;function Gc(e,t,n){if(Qi)return e(t,n);Qi=!0;try{return Kc(e,t,n)}finally{Qi=!1,(Pn!==null||Tn!==null)&&(Qc(),Vc())}}function Cr(e,t){var n=e.stateNode;if(n===null)return null;var r=xi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(j(231,t,typeof n));return n}var Uo=!1;if(dt)try{var er={};Object.defineProperty(er,"passive",{get:function(){Uo=!0}}),window.addEventListener("test",er,er),window.removeEventListener("test",er,er)}catch{Uo=!1}function op(e,t,n,r,s,i,o,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var mr=!1,Bs=null,Hs=!1,zo=null,lp={onError:function(e){mr=!0,Bs=e}};function ap(e,t,n,r,s,i,o,l,a){mr=!1,Bs=null,op.apply(lp,arguments)}function up(e,t,n,r,s,i,o,l,a){if(ap.apply(this,arguments),mr){if(mr){var u=Bs;mr=!1,Bs=null}else throw Error(j(198));Hs||(Hs=!0,zo=u)}}function an(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Jc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ya(e){if(an(e)!==e)throw Error(j(188))}function cp(e){var t=e.alternate;if(!t){if(t=an(e),t===null)throw Error(j(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return Ya(s),e;if(i===r)return Ya(s),t;i=i.sibling}throw Error(j(188))}if(n.return!==r.return)n=s,r=i;else{for(var o=!1,l=s.child;l;){if(l===n){o=!0,n=s,r=i;break}if(l===r){o=!0,r=s,n=i;break}l=l.sibling}if(!o){for(l=i.child;l;){if(l===n){o=!0,n=i,r=s;break}if(l===r){o=!0,r=i,n=s;break}l=l.sibling}if(!o)throw Error(j(189))}}if(n.alternate!==r)throw Error(j(190))}if(n.tag!==3)throw Error(j(188));return n.stateNode.current===n?e:t}function Yc(e){return e=cp(e),e!==null?Xc(e):null}function Xc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Xc(e);if(t!==null)return t;e=e.sibling}return null}var Zc=Pe.unstable_scheduleCallback,Xa=Pe.unstable_cancelCallback,dp=Pe.unstable_shouldYield,hp=Pe.unstable_requestPaint,X=Pe.unstable_now,fp=Pe.unstable_getCurrentPriorityLevel,Bl=Pe.unstable_ImmediatePriority,ed=Pe.unstable_UserBlockingPriority,Ws=Pe.unstable_NormalPriority,pp=Pe.unstable_LowPriority,td=Pe.unstable_IdlePriority,gi=null,et=null;function mp(e){if(et&&typeof et.onCommitFiberRoot=="function")try{et.onCommitFiberRoot(gi,e,void 0,(e.current.flags&128)===128)}catch{}}var qe=Math.clz32?Math.clz32:yp,gp=Math.log,vp=Math.LN2;function yp(e){return e>>>=0,e===0?32:31-(gp(e)/vp|0)|0}var ls=64,as=4194304;function dr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function qs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var l=o&~s;l!==0?r=dr(l):(i&=o,i!==0&&(r=dr(i)))}else o=n&~s,o!==0?r=dr(o):i!==0&&(r=dr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-qe(t),s=1<<n,r|=e[n],t&=~s;return r}function wp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function xp(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-qe(i),l=1<<o,a=s[o];a===-1?(!(l&n)||l&r)&&(s[o]=wp(l,t)):a<=t&&(e.expiredLanes|=l),i&=~l}}function Fo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function nd(){var e=ls;return ls<<=1,!(ls&4194240)&&(ls=64),e}function Gi(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Vr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-qe(t),e[t]=n}function _p(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-qe(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function Hl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-qe(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var U=0;function rd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var sd,Wl,id,od,ld,Bo=!1,us=[],Ct=null,Pt=null,Tt=null,Pr=new Map,Tr=new Map,kt=[],kp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Za(e,t){switch(e){case"focusin":case"focusout":Ct=null;break;case"dragenter":case"dragleave":Pt=null;break;case"mouseover":case"mouseout":Tt=null;break;case"pointerover":case"pointerout":Pr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Tr.delete(t.pointerId)}}function tr(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=Qr(t),t!==null&&Wl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Sp(e,t,n,r,s){switch(t){case"focusin":return Ct=tr(Ct,e,t,n,r,s),!0;case"dragenter":return Pt=tr(Pt,e,t,n,r,s),!0;case"mouseover":return Tt=tr(Tt,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return Pr.set(i,tr(Pr.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,Tr.set(i,tr(Tr.get(i)||null,e,t,n,r,s)),!0}return!1}function ad(e){var t=Jt(e.target);if(t!==null){var n=an(t);if(n!==null){if(t=n.tag,t===13){if(t=Jc(n),t!==null){e.blockedOn=t,ld(e.priority,function(){id(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Cs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ho(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Do=r,n.target.dispatchEvent(r),Do=null}else return t=Qr(n),t!==null&&Wl(t),e.blockedOn=n,!1;t.shift()}return!0}function eu(e,t,n){Cs(e)&&n.delete(t)}function jp(){Bo=!1,Ct!==null&&Cs(Ct)&&(Ct=null),Pt!==null&&Cs(Pt)&&(Pt=null),Tt!==null&&Cs(Tt)&&(Tt=null),Pr.forEach(eu),Tr.forEach(eu)}function nr(e,t){e.blockedOn===t&&(e.blockedOn=null,Bo||(Bo=!0,Pe.unstable_scheduleCallback(Pe.unstable_NormalPriority,jp)))}function Rr(e){function t(s){return nr(s,e)}if(0<us.length){nr(us[0],e);for(var n=1;n<us.length;n++){var r=us[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Ct!==null&&nr(Ct,e),Pt!==null&&nr(Pt,e),Tt!==null&&nr(Tt,e),Pr.forEach(t),Tr.forEach(t),n=0;n<kt.length;n++)r=kt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<kt.length&&(n=kt[0],n.blockedOn===null);)ad(n),n.blockedOn===null&&kt.shift()}var Rn=gt.ReactCurrentBatchConfig,Vs=!0;function Ep(e,t,n,r){var s=U,i=Rn.transition;Rn.transition=null;try{U=1,ql(e,t,n,r)}finally{U=s,Rn.transition=i}}function bp(e,t,n,r){var s=U,i=Rn.transition;Rn.transition=null;try{U=4,ql(e,t,n,r)}finally{U=s,Rn.transition=i}}function ql(e,t,n,r){if(Vs){var s=Ho(e,t,n,r);if(s===null)io(e,t,r,Ks,n),Za(e,r);else if(Sp(s,e,t,n,r))r.stopPropagation();else if(Za(e,r),t&4&&-1<kp.indexOf(e)){for(;s!==null;){var i=Qr(s);if(i!==null&&sd(i),i=Ho(e,t,n,r),i===null&&io(e,t,r,Ks,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else io(e,t,r,null,n)}}var Ks=null;function Ho(e,t,n,r){if(Ks=null,e=Fl(r),e=Jt(e),e!==null)if(t=an(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Jc(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ks=e,null}function ud(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(fp()){case Bl:return 1;case ed:return 4;case Ws:case pp:return 16;case td:return 536870912;default:return 16}default:return 16}}var Et=null,Vl=null,Ps=null;function cd(){if(Ps)return Ps;var e,t=Vl,n=t.length,r,s="value"in Et?Et.value:Et.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===s[i-r];r++);return Ps=s.slice(e,1<r?1-r:void 0)}function Ts(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function cs(){return!0}function tu(){return!1}function Re(e){function t(n,r,s,i,o){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?cs:tu,this.isPropagationStopped=tu,this}return Q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=cs)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=cs)},persist:function(){},isPersistent:cs}),t}var qn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Kl=Re(qn),Kr=Q({},qn,{view:0,detail:0}),Np=Re(Kr),Ji,Yi,rr,vi=Q({},Kr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ql,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==rr&&(rr&&e.type==="mousemove"?(Ji=e.screenX-rr.screenX,Yi=e.screenY-rr.screenY):Yi=Ji=0,rr=e),Ji)},movementY:function(e){return"movementY"in e?e.movementY:Yi}}),nu=Re(vi),Cp=Q({},vi,{dataTransfer:0}),Pp=Re(Cp),Tp=Q({},Kr,{relatedTarget:0}),Xi=Re(Tp),Rp=Q({},qn,{animationName:0,elapsedTime:0,pseudoElement:0}),Op=Re(Rp),Lp=Q({},qn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),$p=Re(Lp),Ip=Q({},qn,{data:0}),ru=Re(Ip),Ap={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Dp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Mp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Up(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Mp[e])?!!t[e]:!1}function Ql(){return Up}var zp=Q({},Kr,{key:function(e){if(e.key){var t=Ap[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ts(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Dp[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ql,charCode:function(e){return e.type==="keypress"?Ts(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ts(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Fp=Re(zp),Bp=Q({},vi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),su=Re(Bp),Hp=Q({},Kr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ql}),Wp=Re(Hp),qp=Q({},qn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Vp=Re(qp),Kp=Q({},vi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Qp=Re(Kp),Gp=[9,13,27,32],Gl=dt&&"CompositionEvent"in window,gr=null;dt&&"documentMode"in document&&(gr=document.documentMode);var Jp=dt&&"TextEvent"in window&&!gr,dd=dt&&(!Gl||gr&&8<gr&&11>=gr),iu=" ",ou=!1;function hd(e,t){switch(e){case"keyup":return Gp.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function fd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var yn=!1;function Yp(e,t){switch(e){case"compositionend":return fd(t);case"keypress":return t.which!==32?null:(ou=!0,iu);case"textInput":return e=t.data,e===iu&&ou?null:e;default:return null}}function Xp(e,t){if(yn)return e==="compositionend"||!Gl&&hd(e,t)?(e=cd(),Ps=Vl=Et=null,yn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return dd&&t.locale!=="ko"?null:t.data;default:return null}}var Zp={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function lu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Zp[e.type]:t==="textarea"}function pd(e,t,n,r){qc(r),t=Qs(t,"onChange"),0<t.length&&(n=new Kl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var vr=null,Or=null;function em(e){Ed(e,0)}function yi(e){var t=_n(e);if(Mc(t))return e}function tm(e,t){if(e==="change")return t}var md=!1;if(dt){var Zi;if(dt){var eo="oninput"in document;if(!eo){var au=document.createElement("div");au.setAttribute("oninput","return;"),eo=typeof au.oninput=="function"}Zi=eo}else Zi=!1;md=Zi&&(!document.documentMode||9<document.documentMode)}function uu(){vr&&(vr.detachEvent("onpropertychange",gd),Or=vr=null)}function gd(e){if(e.propertyName==="value"&&yi(Or)){var t=[];pd(t,Or,e,Fl(e)),Gc(em,t)}}function nm(e,t,n){e==="focusin"?(uu(),vr=t,Or=n,vr.attachEvent("onpropertychange",gd)):e==="focusout"&&uu()}function rm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return yi(Or)}function sm(e,t){if(e==="click")return yi(t)}function im(e,t){if(e==="input"||e==="change")return yi(t)}function om(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ke=typeof Object.is=="function"?Object.is:om;function Lr(e,t){if(Ke(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Eo.call(t,s)||!Ke(e[s],t[s]))return!1}return!0}function cu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function du(e,t){var n=cu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=cu(n)}}function vd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?vd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function yd(){for(var e=window,t=Fs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Fs(e.document)}return t}function Jl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function lm(e){var t=yd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&vd(n.ownerDocument.documentElement,n)){if(r!==null&&Jl(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=du(n,i);var o=du(n,r);s&&o&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var am=dt&&"documentMode"in document&&11>=document.documentMode,wn=null,Wo=null,yr=null,qo=!1;function hu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;qo||wn==null||wn!==Fs(r)||(r=wn,"selectionStart"in r&&Jl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),yr&&Lr(yr,r)||(yr=r,r=Qs(Wo,"onSelect"),0<r.length&&(t=new Kl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=wn)))}function ds(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var xn={animationend:ds("Animation","AnimationEnd"),animationiteration:ds("Animation","AnimationIteration"),animationstart:ds("Animation","AnimationStart"),transitionend:ds("Transition","TransitionEnd")},to={},wd={};dt&&(wd=document.createElement("div").style,"AnimationEvent"in window||(delete xn.animationend.animation,delete xn.animationiteration.animation,delete xn.animationstart.animation),"TransitionEvent"in window||delete xn.transitionend.transition);function wi(e){if(to[e])return to[e];if(!xn[e])return e;var t=xn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in wd)return to[e]=t[n];return e}var xd=wi("animationend"),_d=wi("animationiteration"),kd=wi("animationstart"),Sd=wi("transitionend"),jd=new Map,fu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Mt(e,t){jd.set(e,t),ln(t,[e])}for(var no=0;no<fu.length;no++){var ro=fu[no],um=ro.toLowerCase(),cm=ro[0].toUpperCase()+ro.slice(1);Mt(um,"on"+cm)}Mt(xd,"onAnimationEnd");Mt(_d,"onAnimationIteration");Mt(kd,"onAnimationStart");Mt("dblclick","onDoubleClick");Mt("focusin","onFocus");Mt("focusout","onBlur");Mt(Sd,"onTransitionEnd");In("onMouseEnter",["mouseout","mouseover"]);In("onMouseLeave",["mouseout","mouseover"]);In("onPointerEnter",["pointerout","pointerover"]);In("onPointerLeave",["pointerout","pointerover"]);ln("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));ln("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));ln("onBeforeInput",["compositionend","keypress","textInput","paste"]);ln("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));ln("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));ln("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var hr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),dm=new Set("cancel close invalid load scroll toggle".split(" ").concat(hr));function pu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,up(r,t,void 0,e),e.currentTarget=null}function Ed(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var l=r[o],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==i&&s.isPropagationStopped())break e;pu(s,l,u),i=a}else for(o=0;o<r.length;o++){if(l=r[o],a=l.instance,u=l.currentTarget,l=l.listener,a!==i&&s.isPropagationStopped())break e;pu(s,l,u),i=a}}}if(Hs)throw e=zo,Hs=!1,zo=null,e}function B(e,t){var n=t[Jo];n===void 0&&(n=t[Jo]=new Set);var r=e+"__bubble";n.has(r)||(bd(t,e,2,!1),n.add(r))}function so(e,t,n){var r=0;t&&(r|=4),bd(n,e,r,t)}var hs="_reactListening"+Math.random().toString(36).slice(2);function $r(e){if(!e[hs]){e[hs]=!0,Lc.forEach(function(n){n!=="selectionchange"&&(dm.has(n)||so(n,!1,e),so(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[hs]||(t[hs]=!0,so("selectionchange",!1,t))}}function bd(e,t,n,r){switch(ud(t)){case 1:var s=Ep;break;case 4:s=bp;break;default:s=ql}n=s.bind(null,t,n,e),s=void 0,!Uo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function io(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var l=r.stateNode.containerInfo;if(l===s||l.nodeType===8&&l.parentNode===s)break;if(o===4)for(o=r.return;o!==null;){var a=o.tag;if((a===3||a===4)&&(a=o.stateNode.containerInfo,a===s||a.nodeType===8&&a.parentNode===s))return;o=o.return}for(;l!==null;){if(o=Jt(l),o===null)return;if(a=o.tag,a===5||a===6){r=i=o;continue e}l=l.parentNode}}r=r.return}Gc(function(){var u=i,d=Fl(n),h=[];e:{var f=jd.get(e);if(f!==void 0){var m=Kl,y=e;switch(e){case"keypress":if(Ts(n)===0)break e;case"keydown":case"keyup":m=Fp;break;case"focusin":y="focus",m=Xi;break;case"focusout":y="blur",m=Xi;break;case"beforeblur":case"afterblur":m=Xi;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=nu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=Pp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=Wp;break;case xd:case _d:case kd:m=Op;break;case Sd:m=Vp;break;case"scroll":m=Np;break;case"wheel":m=Qp;break;case"copy":case"cut":case"paste":m=$p;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=su}var w=(t&4)!==0,k=!w&&e==="scroll",g=w?f!==null?f+"Capture":null:f;w=[];for(var p=u,v;p!==null;){v=p;var _=v.stateNode;if(v.tag===5&&_!==null&&(v=_,g!==null&&(_=Cr(p,g),_!=null&&w.push(Ir(p,_,v)))),k)break;p=p.return}0<w.length&&(f=new m(f,y,null,n,d),h.push({event:f,listeners:w}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",f&&n!==Do&&(y=n.relatedTarget||n.fromElement)&&(Jt(y)||y[ht]))break e;if((m||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=u,y=y?Jt(y):null,y!==null&&(k=an(y),y!==k||y.tag!==5&&y.tag!==6)&&(y=null)):(m=null,y=u),m!==y)){if(w=nu,_="onMouseLeave",g="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(w=su,_="onPointerLeave",g="onPointerEnter",p="pointer"),k=m==null?f:_n(m),v=y==null?f:_n(y),f=new w(_,p+"leave",m,n,d),f.target=k,f.relatedTarget=v,_=null,Jt(d)===u&&(w=new w(g,p+"enter",y,n,d),w.target=v,w.relatedTarget=k,_=w),k=_,m&&y)t:{for(w=m,g=y,p=0,v=w;v;v=cn(v))p++;for(v=0,_=g;_;_=cn(_))v++;for(;0<p-v;)w=cn(w),p--;for(;0<v-p;)g=cn(g),v--;for(;p--;){if(w===g||g!==null&&w===g.alternate)break t;w=cn(w),g=cn(g)}w=null}else w=null;m!==null&&mu(h,f,m,w,!1),y!==null&&k!==null&&mu(h,k,y,w,!0)}}e:{if(f=u?_n(u):window,m=f.nodeName&&f.nodeName.toLowerCase(),m==="select"||m==="input"&&f.type==="file")var S=tm;else if(lu(f))if(md)S=im;else{S=rm;var E=nm}else(m=f.nodeName)&&m.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(S=sm);if(S&&(S=S(e,u))){pd(h,S,n,d);break e}E&&E(e,f,u),e==="focusout"&&(E=f._wrapperState)&&E.controlled&&f.type==="number"&&Oo(f,"number",f.value)}switch(E=u?_n(u):window,e){case"focusin":(lu(E)||E.contentEditable==="true")&&(wn=E,Wo=u,yr=null);break;case"focusout":yr=Wo=wn=null;break;case"mousedown":qo=!0;break;case"contextmenu":case"mouseup":case"dragend":qo=!1,hu(h,n,d);break;case"selectionchange":if(am)break;case"keydown":case"keyup":hu(h,n,d)}var b;if(Gl)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else yn?hd(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(dd&&n.locale!=="ko"&&(yn||T!=="onCompositionStart"?T==="onCompositionEnd"&&yn&&(b=cd()):(Et=d,Vl="value"in Et?Et.value:Et.textContent,yn=!0)),E=Qs(u,T),0<E.length&&(T=new ru(T,e,null,n,d),h.push({event:T,listeners:E}),b?T.data=b:(b=fd(n),b!==null&&(T.data=b)))),(b=Jp?Yp(e,n):Xp(e,n))&&(u=Qs(u,"onBeforeInput"),0<u.length&&(d=new ru("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:u}),d.data=b))}Ed(h,t)})}function Ir(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qs(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Cr(e,n),i!=null&&r.unshift(Ir(e,i,s)),i=Cr(e,t),i!=null&&r.push(Ir(e,i,s))),e=e.return}return r}function cn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function mu(e,t,n,r,s){for(var i=t._reactName,o=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,s?(a=Cr(n,i),a!=null&&o.unshift(Ir(n,a,l))):s||(a=Cr(n,i),a!=null&&o.push(Ir(n,a,l)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var hm=/\r\n?/g,fm=/\u0000|\uFFFD/g;function gu(e){return(typeof e=="string"?e:""+e).replace(hm,`
`).replace(fm,"")}function fs(e,t,n){if(t=gu(t),gu(e)!==t&&n)throw Error(j(425))}function Gs(){}var Vo=null,Ko=null;function Qo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Go=typeof setTimeout=="function"?setTimeout:void 0,pm=typeof clearTimeout=="function"?clearTimeout:void 0,vu=typeof Promise=="function"?Promise:void 0,mm=typeof queueMicrotask=="function"?queueMicrotask:typeof vu<"u"?function(e){return vu.resolve(null).then(e).catch(gm)}:Go;function gm(e){setTimeout(function(){throw e})}function oo(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),Rr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);Rr(t)}function Rt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function yu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Vn=Math.random().toString(36).slice(2),Ze="__reactFiber$"+Vn,Ar="__reactProps$"+Vn,ht="__reactContainer$"+Vn,Jo="__reactEvents$"+Vn,vm="__reactListeners$"+Vn,ym="__reactHandles$"+Vn;function Jt(e){var t=e[Ze];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ht]||n[Ze]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=yu(e);e!==null;){if(n=e[Ze])return n;e=yu(e)}return t}e=n,n=e.parentNode}return null}function Qr(e){return e=e[Ze]||e[ht],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function _n(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(j(33))}function xi(e){return e[Ar]||null}var Yo=[],kn=-1;function Ut(e){return{current:e}}function H(e){0>kn||(e.current=Yo[kn],Yo[kn]=null,kn--)}function F(e,t){kn++,Yo[kn]=e.current,e.current=t}var Dt={},he=Ut(Dt),Se=Ut(!1),tn=Dt;function An(e,t){var n=e.type.contextTypes;if(!n)return Dt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function je(e){return e=e.childContextTypes,e!=null}function Js(){H(Se),H(he)}function wu(e,t,n){if(he.current!==Dt)throw Error(j(168));F(he,t),F(Se,n)}function Nd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(j(108,np(e)||"Unknown",s));return Q({},n,r)}function Ys(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Dt,tn=he.current,F(he,e),F(Se,Se.current),!0}function xu(e,t,n){var r=e.stateNode;if(!r)throw Error(j(169));n?(e=Nd(e,t,tn),r.__reactInternalMemoizedMergedChildContext=e,H(Se),H(he),F(he,e)):H(Se),F(Se,n)}var it=null,_i=!1,lo=!1;function Cd(e){it===null?it=[e]:it.push(e)}function wm(e){_i=!0,Cd(e)}function zt(){if(!lo&&it!==null){lo=!0;var e=0,t=U;try{var n=it;for(U=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}it=null,_i=!1}catch(s){throw it!==null&&(it=it.slice(e+1)),Zc(Bl,zt),s}finally{U=t,lo=!1}}return null}var Sn=[],jn=0,Xs=null,Zs=0,Oe=[],Le=0,nn=null,ot=1,lt="";function Vt(e,t){Sn[jn++]=Zs,Sn[jn++]=Xs,Xs=e,Zs=t}function Pd(e,t,n){Oe[Le++]=ot,Oe[Le++]=lt,Oe[Le++]=nn,nn=e;var r=ot;e=lt;var s=32-qe(r)-1;r&=~(1<<s),n+=1;var i=32-qe(t)+s;if(30<i){var o=s-s%5;i=(r&(1<<o)-1).toString(32),r>>=o,s-=o,ot=1<<32-qe(t)+s|n<<s|r,lt=i+e}else ot=1<<i|n<<s|r,lt=e}function Yl(e){e.return!==null&&(Vt(e,1),Pd(e,1,0))}function Xl(e){for(;e===Xs;)Xs=Sn[--jn],Sn[jn]=null,Zs=Sn[--jn],Sn[jn]=null;for(;e===nn;)nn=Oe[--Le],Oe[Le]=null,lt=Oe[--Le],Oe[Le]=null,ot=Oe[--Le],Oe[Le]=null}var Ce=null,Ne=null,W=!1,We=null;function Td(e,t){var n=$e(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function _u(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ce=e,Ne=Rt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ce=e,Ne=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=nn!==null?{id:ot,overflow:lt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=$e(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ce=e,Ne=null,!0):!1;default:return!1}}function Xo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Zo(e){if(W){var t=Ne;if(t){var n=t;if(!_u(e,t)){if(Xo(e))throw Error(j(418));t=Rt(n.nextSibling);var r=Ce;t&&_u(e,t)?Td(r,n):(e.flags=e.flags&-4097|2,W=!1,Ce=e)}}else{if(Xo(e))throw Error(j(418));e.flags=e.flags&-4097|2,W=!1,Ce=e}}}function ku(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ce=e}function ps(e){if(e!==Ce)return!1;if(!W)return ku(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Qo(e.type,e.memoizedProps)),t&&(t=Ne)){if(Xo(e))throw Rd(),Error(j(418));for(;t;)Td(e,t),t=Rt(t.nextSibling)}if(ku(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(j(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ne=Rt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ne=null}}else Ne=Ce?Rt(e.stateNode.nextSibling):null;return!0}function Rd(){for(var e=Ne;e;)e=Rt(e.nextSibling)}function Dn(){Ne=Ce=null,W=!1}function Zl(e){We===null?We=[e]:We.push(e)}var xm=gt.ReactCurrentBatchConfig;function sr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(j(309));var r=n.stateNode}if(!r)throw Error(j(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var l=s.refs;o===null?delete l[i]:l[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(j(284));if(!n._owner)throw Error(j(290,e))}return e}function ms(e,t){throw e=Object.prototype.toString.call(t),Error(j(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Su(e){var t=e._init;return t(e._payload)}function Od(e){function t(g,p){if(e){var v=g.deletions;v===null?(g.deletions=[p],g.flags|=16):v.push(p)}}function n(g,p){if(!e)return null;for(;p!==null;)t(g,p),p=p.sibling;return null}function r(g,p){for(g=new Map;p!==null;)p.key!==null?g.set(p.key,p):g.set(p.index,p),p=p.sibling;return g}function s(g,p){return g=It(g,p),g.index=0,g.sibling=null,g}function i(g,p,v){return g.index=v,e?(v=g.alternate,v!==null?(v=v.index,v<p?(g.flags|=2,p):v):(g.flags|=2,p)):(g.flags|=1048576,p)}function o(g){return e&&g.alternate===null&&(g.flags|=2),g}function l(g,p,v,_){return p===null||p.tag!==6?(p=mo(v,g.mode,_),p.return=g,p):(p=s(p,v),p.return=g,p)}function a(g,p,v,_){var S=v.type;return S===vn?d(g,p,v.props.children,_,v.key):p!==null&&(p.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===wt&&Su(S)===p.type)?(_=s(p,v.props),_.ref=sr(g,p,v),_.return=g,_):(_=Ds(v.type,v.key,v.props,null,g.mode,_),_.ref=sr(g,p,v),_.return=g,_)}function u(g,p,v,_){return p===null||p.tag!==4||p.stateNode.containerInfo!==v.containerInfo||p.stateNode.implementation!==v.implementation?(p=go(v,g.mode,_),p.return=g,p):(p=s(p,v.children||[]),p.return=g,p)}function d(g,p,v,_,S){return p===null||p.tag!==7?(p=en(v,g.mode,_,S),p.return=g,p):(p=s(p,v),p.return=g,p)}function h(g,p,v){if(typeof p=="string"&&p!==""||typeof p=="number")return p=mo(""+p,g.mode,v),p.return=g,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case ss:return v=Ds(p.type,p.key,p.props,null,g.mode,v),v.ref=sr(g,null,p),v.return=g,v;case gn:return p=go(p,g.mode,v),p.return=g,p;case wt:var _=p._init;return h(g,_(p._payload),v)}if(cr(p)||Zn(p))return p=en(p,g.mode,v,null),p.return=g,p;ms(g,p)}return null}function f(g,p,v,_){var S=p!==null?p.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return S!==null?null:l(g,p,""+v,_);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case ss:return v.key===S?a(g,p,v,_):null;case gn:return v.key===S?u(g,p,v,_):null;case wt:return S=v._init,f(g,p,S(v._payload),_)}if(cr(v)||Zn(v))return S!==null?null:d(g,p,v,_,null);ms(g,v)}return null}function m(g,p,v,_,S){if(typeof _=="string"&&_!==""||typeof _=="number")return g=g.get(v)||null,l(p,g,""+_,S);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case ss:return g=g.get(_.key===null?v:_.key)||null,a(p,g,_,S);case gn:return g=g.get(_.key===null?v:_.key)||null,u(p,g,_,S);case wt:var E=_._init;return m(g,p,v,E(_._payload),S)}if(cr(_)||Zn(_))return g=g.get(v)||null,d(p,g,_,S,null);ms(p,_)}return null}function y(g,p,v,_){for(var S=null,E=null,b=p,T=p=0,D=null;b!==null&&T<v.length;T++){b.index>T?(D=b,b=null):D=b.sibling;var R=f(g,b,v[T],_);if(R===null){b===null&&(b=D);break}e&&b&&R.alternate===null&&t(g,b),p=i(R,p,T),E===null?S=R:E.sibling=R,E=R,b=D}if(T===v.length)return n(g,b),W&&Vt(g,T),S;if(b===null){for(;T<v.length;T++)b=h(g,v[T],_),b!==null&&(p=i(b,p,T),E===null?S=b:E.sibling=b,E=b);return W&&Vt(g,T),S}for(b=r(g,b);T<v.length;T++)D=m(b,g,T,v[T],_),D!==null&&(e&&D.alternate!==null&&b.delete(D.key===null?T:D.key),p=i(D,p,T),E===null?S=D:E.sibling=D,E=D);return e&&b.forEach(function(N){return t(g,N)}),W&&Vt(g,T),S}function w(g,p,v,_){var S=Zn(v);if(typeof S!="function")throw Error(j(150));if(v=S.call(v),v==null)throw Error(j(151));for(var E=S=null,b=p,T=p=0,D=null,R=v.next();b!==null&&!R.done;T++,R=v.next()){b.index>T?(D=b,b=null):D=b.sibling;var N=f(g,b,R.value,_);if(N===null){b===null&&(b=D);break}e&&b&&N.alternate===null&&t(g,b),p=i(N,p,T),E===null?S=N:E.sibling=N,E=N,b=D}if(R.done)return n(g,b),W&&Vt(g,T),S;if(b===null){for(;!R.done;T++,R=v.next())R=h(g,R.value,_),R!==null&&(p=i(R,p,T),E===null?S=R:E.sibling=R,E=R);return W&&Vt(g,T),S}for(b=r(g,b);!R.done;T++,R=v.next())R=m(b,g,T,R.value,_),R!==null&&(e&&R.alternate!==null&&b.delete(R.key===null?T:R.key),p=i(R,p,T),E===null?S=R:E.sibling=R,E=R);return e&&b.forEach(function(fe){return t(g,fe)}),W&&Vt(g,T),S}function k(g,p,v,_){if(typeof v=="object"&&v!==null&&v.type===vn&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case ss:e:{for(var S=v.key,E=p;E!==null;){if(E.key===S){if(S=v.type,S===vn){if(E.tag===7){n(g,E.sibling),p=s(E,v.props.children),p.return=g,g=p;break e}}else if(E.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===wt&&Su(S)===E.type){n(g,E.sibling),p=s(E,v.props),p.ref=sr(g,E,v),p.return=g,g=p;break e}n(g,E);break}else t(g,E);E=E.sibling}v.type===vn?(p=en(v.props.children,g.mode,_,v.key),p.return=g,g=p):(_=Ds(v.type,v.key,v.props,null,g.mode,_),_.ref=sr(g,p,v),_.return=g,g=_)}return o(g);case gn:e:{for(E=v.key;p!==null;){if(p.key===E)if(p.tag===4&&p.stateNode.containerInfo===v.containerInfo&&p.stateNode.implementation===v.implementation){n(g,p.sibling),p=s(p,v.children||[]),p.return=g,g=p;break e}else{n(g,p);break}else t(g,p);p=p.sibling}p=go(v,g.mode,_),p.return=g,g=p}return o(g);case wt:return E=v._init,k(g,p,E(v._payload),_)}if(cr(v))return y(g,p,v,_);if(Zn(v))return w(g,p,v,_);ms(g,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,p!==null&&p.tag===6?(n(g,p.sibling),p=s(p,v),p.return=g,g=p):(n(g,p),p=mo(v,g.mode,_),p.return=g,g=p),o(g)):n(g,p)}return k}var Mn=Od(!0),Ld=Od(!1),ei=Ut(null),ti=null,En=null,ea=null;function ta(){ea=En=ti=null}function na(e){var t=ei.current;H(ei),e._currentValue=t}function el(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function On(e,t){ti=e,ea=En=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ke=!0),e.firstContext=null)}function De(e){var t=e._currentValue;if(ea!==e)if(e={context:e,memoizedValue:t,next:null},En===null){if(ti===null)throw Error(j(308));En=e,ti.dependencies={lanes:0,firstContext:e}}else En=En.next=e;return t}var Yt=null;function ra(e){Yt===null?Yt=[e]:Yt.push(e)}function $d(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,ra(t)):(n.next=s.next,s.next=n),t.interleaved=n,ft(e,r)}function ft(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var xt=!1;function sa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Id(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ut(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ot(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,M&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,ft(e,n)}return s=r.interleaved,s===null?(t.next=t,ra(r)):(t.next=s.next,s.next=t),r.interleaved=t,ft(e,n)}function Rs(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Hl(e,n)}}function ju(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ni(e,t,n,r){var s=e.updateQueue;xt=!1;var i=s.firstBaseUpdate,o=s.lastBaseUpdate,l=s.shared.pending;if(l!==null){s.shared.pending=null;var a=l,u=a.next;a.next=null,o===null?i=u:o.next=u,o=a;var d=e.alternate;d!==null&&(d=d.updateQueue,l=d.lastBaseUpdate,l!==o&&(l===null?d.firstBaseUpdate=u:l.next=u,d.lastBaseUpdate=a))}if(i!==null){var h=s.baseState;o=0,d=u=a=null,l=i;do{var f=l.lane,m=l.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:m,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var y=e,w=l;switch(f=t,m=n,w.tag){case 1:if(y=w.payload,typeof y=="function"){h=y.call(m,h,f);break e}h=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=w.payload,f=typeof y=="function"?y.call(m,h,f):y,f==null)break e;h=Q({},h,f);break e;case 2:xt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,f=s.effects,f===null?s.effects=[l]:f.push(l))}else m={eventTime:m,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},d===null?(u=d=m,a=h):d=d.next=m,o|=f;if(l=l.next,l===null){if(l=s.shared.pending,l===null)break;f=l,l=f.next,f.next=null,s.lastBaseUpdate=f,s.shared.pending=null}}while(!0);if(d===null&&(a=h),s.baseState=a,s.firstBaseUpdate=u,s.lastBaseUpdate=d,t=s.shared.interleaved,t!==null){s=t;do o|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);sn|=o,e.lanes=o,e.memoizedState=h}}function Eu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(j(191,s));s.call(r)}}}var Gr={},tt=Ut(Gr),Dr=Ut(Gr),Mr=Ut(Gr);function Xt(e){if(e===Gr)throw Error(j(174));return e}function ia(e,t){switch(F(Mr,t),F(Dr,e),F(tt,Gr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:$o(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=$o(t,e)}H(tt),F(tt,t)}function Un(){H(tt),H(Dr),H(Mr)}function Ad(e){Xt(Mr.current);var t=Xt(tt.current),n=$o(t,e.type);t!==n&&(F(Dr,e),F(tt,n))}function oa(e){Dr.current===e&&(H(tt),H(Dr))}var V=Ut(0);function ri(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ao=[];function la(){for(var e=0;e<ao.length;e++)ao[e]._workInProgressVersionPrimary=null;ao.length=0}var Os=gt.ReactCurrentDispatcher,uo=gt.ReactCurrentBatchConfig,rn=0,K=null,ee=null,se=null,si=!1,wr=!1,Ur=0,_m=0;function ue(){throw Error(j(321))}function aa(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ke(e[n],t[n]))return!1;return!0}function ua(e,t,n,r,s,i){if(rn=i,K=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Os.current=e===null||e.memoizedState===null?Em:bm,e=n(r,s),wr){i=0;do{if(wr=!1,Ur=0,25<=i)throw Error(j(301));i+=1,se=ee=null,t.updateQueue=null,Os.current=Nm,e=n(r,s)}while(wr)}if(Os.current=ii,t=ee!==null&&ee.next!==null,rn=0,se=ee=K=null,si=!1,t)throw Error(j(300));return e}function ca(){var e=Ur!==0;return Ur=0,e}function Xe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return se===null?K.memoizedState=se=e:se=se.next=e,se}function Me(){if(ee===null){var e=K.alternate;e=e!==null?e.memoizedState:null}else e=ee.next;var t=se===null?K.memoizedState:se.next;if(t!==null)se=t,ee=e;else{if(e===null)throw Error(j(310));ee=e,e={memoizedState:ee.memoizedState,baseState:ee.baseState,baseQueue:ee.baseQueue,queue:ee.queue,next:null},se===null?K.memoizedState=se=e:se=se.next=e}return se}function zr(e,t){return typeof t=="function"?t(e):t}function co(e){var t=Me(),n=t.queue;if(n===null)throw Error(j(311));n.lastRenderedReducer=e;var r=ee,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var o=s.next;s.next=i.next,i.next=o}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var l=o=null,a=null,u=i;do{var d=u.lane;if((rn&d)===d)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var h={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=h,o=r):a=a.next=h,K.lanes|=d,sn|=d}u=u.next}while(u!==null&&u!==i);a===null?o=r:a.next=l,Ke(r,t.memoizedState)||(ke=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,K.lanes|=i,sn|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ho(e){var t=Me(),n=t.queue;if(n===null)throw Error(j(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do i=e(i,o.action),o=o.next;while(o!==s);Ke(i,t.memoizedState)||(ke=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Dd(){}function Md(e,t){var n=K,r=Me(),s=t(),i=!Ke(r.memoizedState,s);if(i&&(r.memoizedState=s,ke=!0),r=r.queue,da(Fd.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||se!==null&&se.memoizedState.tag&1){if(n.flags|=2048,Fr(9,zd.bind(null,n,r,s,t),void 0,null),ie===null)throw Error(j(349));rn&30||Ud(n,t,s)}return s}function Ud(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function zd(e,t,n,r){t.value=n,t.getSnapshot=r,Bd(t)&&Hd(e)}function Fd(e,t,n){return n(function(){Bd(t)&&Hd(e)})}function Bd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ke(e,n)}catch{return!0}}function Hd(e){var t=ft(e,1);t!==null&&Ve(t,e,1,-1)}function bu(e){var t=Xe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:zr,lastRenderedState:e},t.queue=e,e=e.dispatch=jm.bind(null,K,e),[t.memoizedState,e]}function Fr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Wd(){return Me().memoizedState}function Ls(e,t,n,r){var s=Xe();K.flags|=e,s.memoizedState=Fr(1|t,n,void 0,r===void 0?null:r)}function ki(e,t,n,r){var s=Me();r=r===void 0?null:r;var i=void 0;if(ee!==null){var o=ee.memoizedState;if(i=o.destroy,r!==null&&aa(r,o.deps)){s.memoizedState=Fr(t,n,i,r);return}}K.flags|=e,s.memoizedState=Fr(1|t,n,i,r)}function Nu(e,t){return Ls(8390656,8,e,t)}function da(e,t){return ki(2048,8,e,t)}function qd(e,t){return ki(4,2,e,t)}function Vd(e,t){return ki(4,4,e,t)}function Kd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Qd(e,t,n){return n=n!=null?n.concat([e]):null,ki(4,4,Kd.bind(null,t,e),n)}function ha(){}function Gd(e,t){var n=Me();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&aa(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Jd(e,t){var n=Me();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&aa(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Yd(e,t,n){return rn&21?(Ke(n,t)||(n=nd(),K.lanes|=n,sn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ke=!0),e.memoizedState=n)}function km(e,t){var n=U;U=n!==0&&4>n?n:4,e(!0);var r=uo.transition;uo.transition={};try{e(!1),t()}finally{U=n,uo.transition=r}}function Xd(){return Me().memoizedState}function Sm(e,t,n){var r=$t(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Zd(e))eh(t,n);else if(n=$d(e,t,n,r),n!==null){var s=ge();Ve(n,e,r,s),th(n,t,r)}}function jm(e,t,n){var r=$t(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Zd(e))eh(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,l=i(o,n);if(s.hasEagerState=!0,s.eagerState=l,Ke(l,o)){var a=t.interleaved;a===null?(s.next=s,ra(t)):(s.next=a.next,a.next=s),t.interleaved=s;return}}catch{}finally{}n=$d(e,t,s,r),n!==null&&(s=ge(),Ve(n,e,r,s),th(n,t,r))}}function Zd(e){var t=e.alternate;return e===K||t!==null&&t===K}function eh(e,t){wr=si=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function th(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Hl(e,n)}}var ii={readContext:De,useCallback:ue,useContext:ue,useEffect:ue,useImperativeHandle:ue,useInsertionEffect:ue,useLayoutEffect:ue,useMemo:ue,useReducer:ue,useRef:ue,useState:ue,useDebugValue:ue,useDeferredValue:ue,useTransition:ue,useMutableSource:ue,useSyncExternalStore:ue,useId:ue,unstable_isNewReconciler:!1},Em={readContext:De,useCallback:function(e,t){return Xe().memoizedState=[e,t===void 0?null:t],e},useContext:De,useEffect:Nu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Ls(4194308,4,Kd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ls(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ls(4,2,e,t)},useMemo:function(e,t){var n=Xe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Xe();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Sm.bind(null,K,e),[r.memoizedState,e]},useRef:function(e){var t=Xe();return e={current:e},t.memoizedState=e},useState:bu,useDebugValue:ha,useDeferredValue:function(e){return Xe().memoizedState=e},useTransition:function(){var e=bu(!1),t=e[0];return e=km.bind(null,e[1]),Xe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=K,s=Xe();if(W){if(n===void 0)throw Error(j(407));n=n()}else{if(n=t(),ie===null)throw Error(j(349));rn&30||Ud(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,Nu(Fd.bind(null,r,i,e),[e]),r.flags|=2048,Fr(9,zd.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Xe(),t=ie.identifierPrefix;if(W){var n=lt,r=ot;n=(r&~(1<<32-qe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Ur++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=_m++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},bm={readContext:De,useCallback:Gd,useContext:De,useEffect:da,useImperativeHandle:Qd,useInsertionEffect:qd,useLayoutEffect:Vd,useMemo:Jd,useReducer:co,useRef:Wd,useState:function(){return co(zr)},useDebugValue:ha,useDeferredValue:function(e){var t=Me();return Yd(t,ee.memoizedState,e)},useTransition:function(){var e=co(zr)[0],t=Me().memoizedState;return[e,t]},useMutableSource:Dd,useSyncExternalStore:Md,useId:Xd,unstable_isNewReconciler:!1},Nm={readContext:De,useCallback:Gd,useContext:De,useEffect:da,useImperativeHandle:Qd,useInsertionEffect:qd,useLayoutEffect:Vd,useMemo:Jd,useReducer:ho,useRef:Wd,useState:function(){return ho(zr)},useDebugValue:ha,useDeferredValue:function(e){var t=Me();return ee===null?t.memoizedState=e:Yd(t,ee.memoizedState,e)},useTransition:function(){var e=ho(zr)[0],t=Me().memoizedState;return[e,t]},useMutableSource:Dd,useSyncExternalStore:Md,useId:Xd,unstable_isNewReconciler:!1};function Fe(e,t){if(e&&e.defaultProps){t=Q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function tl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Si={isMounted:function(e){return(e=e._reactInternals)?an(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ge(),s=$t(e),i=ut(r,s);i.payload=t,n!=null&&(i.callback=n),t=Ot(e,i,s),t!==null&&(Ve(t,e,s,r),Rs(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ge(),s=$t(e),i=ut(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Ot(e,i,s),t!==null&&(Ve(t,e,s,r),Rs(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ge(),r=$t(e),s=ut(n,r);s.tag=2,t!=null&&(s.callback=t),t=Ot(e,s,r),t!==null&&(Ve(t,e,r,n),Rs(t,e,r))}};function Cu(e,t,n,r,s,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!Lr(n,r)||!Lr(s,i):!0}function nh(e,t,n){var r=!1,s=Dt,i=t.contextType;return typeof i=="object"&&i!==null?i=De(i):(s=je(t)?tn:he.current,r=t.contextTypes,i=(r=r!=null)?An(e,s):Dt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Si,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function Pu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Si.enqueueReplaceState(t,t.state,null)}function nl(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},sa(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=De(i):(i=je(t)?tn:he.current,s.context=An(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(tl(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&Si.enqueueReplaceState(s,s.state,null),ni(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function zn(e,t){try{var n="",r=t;do n+=tp(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function fo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function rl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Cm=typeof WeakMap=="function"?WeakMap:Map;function rh(e,t,n){n=ut(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){li||(li=!0,fl=r),rl(e,t)},n}function sh(e,t,n){n=ut(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){rl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){rl(e,t),typeof r!="function"&&(Lt===null?Lt=new Set([this]):Lt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Tu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Cm;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=Bm.bind(null,e,t,n),t.then(e,e))}function Ru(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ou(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ut(-1,1),t.tag=2,Ot(n,t,1))),n.lanes|=1),e)}var Pm=gt.ReactCurrentOwner,ke=!1;function me(e,t,n,r){t.child=e===null?Ld(t,null,n,r):Mn(t,e.child,n,r)}function Lu(e,t,n,r,s){n=n.render;var i=t.ref;return On(t,s),r=ua(e,t,n,r,i,s),n=ca(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,pt(e,t,s)):(W&&n&&Yl(t),t.flags|=1,me(e,t,r,s),t.child)}function $u(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!xa(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,ih(e,t,i,r,s)):(e=Ds(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:Lr,n(o,r)&&e.ref===t.ref)return pt(e,t,s)}return t.flags|=1,e=It(i,r),e.ref=t.ref,e.return=t,t.child=e}function ih(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(Lr(i,r)&&e.ref===t.ref)if(ke=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(ke=!0);else return t.lanes=e.lanes,pt(e,t,s)}return sl(e,t,n,r,s)}function oh(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},F(Nn,be),be|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,F(Nn,be),be|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,F(Nn,be),be|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,F(Nn,be),be|=r;return me(e,t,s,n),t.child}function lh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function sl(e,t,n,r,s){var i=je(n)?tn:he.current;return i=An(t,i),On(t,s),n=ua(e,t,n,r,i,s),r=ca(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,pt(e,t,s)):(W&&r&&Yl(t),t.flags|=1,me(e,t,n,s),t.child)}function Iu(e,t,n,r,s){if(je(n)){var i=!0;Ys(t)}else i=!1;if(On(t,s),t.stateNode===null)$s(e,t),nh(t,n,r),nl(t,n,r,s),r=!0;else if(e===null){var o=t.stateNode,l=t.memoizedProps;o.props=l;var a=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=De(u):(u=je(n)?tn:he.current,u=An(t,u));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Pu(t,o,r,u),xt=!1;var f=t.memoizedState;o.state=f,ni(t,r,o,s),a=t.memoizedState,l!==r||f!==a||Se.current||xt?(typeof d=="function"&&(tl(t,n,d,r),a=t.memoizedState),(l=xt||Cu(t,n,l,r,f,a,u))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),o.props=r,o.state=a,o.context=u,r=l):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Id(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:Fe(t.type,l),o.props=u,h=t.pendingProps,f=o.context,a=n.contextType,typeof a=="object"&&a!==null?a=De(a):(a=je(n)?tn:he.current,a=An(t,a));var m=n.getDerivedStateFromProps;(d=typeof m=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==h||f!==a)&&Pu(t,o,r,a),xt=!1,f=t.memoizedState,o.state=f,ni(t,r,o,s);var y=t.memoizedState;l!==h||f!==y||Se.current||xt?(typeof m=="function"&&(tl(t,n,m,r),y=t.memoizedState),(u=xt||Cu(t,n,u,r,f,y,a)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,y,a),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,y,a)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),o.props=r,o.state=y,o.context=a,r=u):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return il(e,t,n,r,i,s)}function il(e,t,n,r,s,i){lh(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return s&&xu(t,n,!1),pt(e,t,i);r=t.stateNode,Pm.current=t;var l=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Mn(t,e.child,null,i),t.child=Mn(t,null,l,i)):me(e,t,l,i),t.memoizedState=r.state,s&&xu(t,n,!0),t.child}function ah(e){var t=e.stateNode;t.pendingContext?wu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&wu(e,t.context,!1),ia(e,t.containerInfo)}function Au(e,t,n,r,s){return Dn(),Zl(s),t.flags|=256,me(e,t,n,r),t.child}var ol={dehydrated:null,treeContext:null,retryLane:0};function ll(e){return{baseLanes:e,cachePool:null,transitions:null}}function uh(e,t,n){var r=t.pendingProps,s=V.current,i=!1,o=(t.flags&128)!==0,l;if((l=o)||(l=e!==null&&e.memoizedState===null?!1:(s&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),F(V,s&1),e===null)return Zo(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=bi(o,r,0,null),e=en(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=ll(n),t.memoizedState=ol,e):fa(t,o));if(s=e.memoizedState,s!==null&&(l=s.dehydrated,l!==null))return Tm(e,t,o,r,l,s,n);if(i){i=r.fallback,o=t.mode,s=e.child,l=s.sibling;var a={mode:"hidden",children:r.children};return!(o&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=It(s,a),r.subtreeFlags=s.subtreeFlags&14680064),l!==null?i=It(l,i):(i=en(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?ll(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=ol,r}return i=e.child,e=i.sibling,r=It(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function fa(e,t){return t=bi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function gs(e,t,n,r){return r!==null&&Zl(r),Mn(t,e.child,null,n),e=fa(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Tm(e,t,n,r,s,i,o){if(n)return t.flags&256?(t.flags&=-257,r=fo(Error(j(422))),gs(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=bi({mode:"visible",children:r.children},s,0,null),i=en(i,s,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Mn(t,e.child,null,o),t.child.memoizedState=ll(o),t.memoizedState=ol,i);if(!(t.mode&1))return gs(e,t,o,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(j(419)),r=fo(i,r,void 0),gs(e,t,o,r)}if(l=(o&e.childLanes)!==0,ke||l){if(r=ie,r!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|o)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,ft(e,s),Ve(r,e,s,-1))}return wa(),r=fo(Error(j(421))),gs(e,t,o,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=Hm.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,Ne=Rt(s.nextSibling),Ce=t,W=!0,We=null,e!==null&&(Oe[Le++]=ot,Oe[Le++]=lt,Oe[Le++]=nn,ot=e.id,lt=e.overflow,nn=t),t=fa(t,r.children),t.flags|=4096,t)}function Du(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),el(e.return,t,n)}function po(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function ch(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(me(e,t,r.children,n),r=V.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Du(e,n,t);else if(e.tag===19)Du(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(F(V,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&ri(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),po(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&ri(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}po(t,!0,n,null,i);break;case"together":po(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function $s(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function pt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),sn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(j(153));if(t.child!==null){for(e=t.child,n=It(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=It(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Rm(e,t,n){switch(t.tag){case 3:ah(t),Dn();break;case 5:Ad(t);break;case 1:je(t.type)&&Ys(t);break;case 4:ia(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;F(ei,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(F(V,V.current&1),t.flags|=128,null):n&t.child.childLanes?uh(e,t,n):(F(V,V.current&1),e=pt(e,t,n),e!==null?e.sibling:null);F(V,V.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ch(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),F(V,V.current),r)break;return null;case 22:case 23:return t.lanes=0,oh(e,t,n)}return pt(e,t,n)}var dh,al,hh,fh;dh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};al=function(){};hh=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,Xt(tt.current);var i=null;switch(n){case"input":s=To(e,s),r=To(e,r),i=[];break;case"select":s=Q({},s,{value:void 0}),r=Q({},r,{value:void 0}),i=[];break;case"textarea":s=Lo(e,s),r=Lo(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Gs)}Io(n,r);var o;n=null;for(u in s)if(!r.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var l=s[u];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(br.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(l=s!=null?s[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(o in l)!l.hasOwnProperty(o)||a&&a.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in a)a.hasOwnProperty(o)&&l[o]!==a[o]&&(n||(n={}),n[o]=a[o])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(br.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&B("scroll",e),i||l===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};fh=function(e,t,n,r){n!==r&&(t.flags|=4)};function ir(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ce(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Om(e,t,n){var r=t.pendingProps;switch(Xl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ce(t),null;case 1:return je(t.type)&&Js(),ce(t),null;case 3:return r=t.stateNode,Un(),H(Se),H(he),la(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ps(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,We!==null&&(gl(We),We=null))),al(e,t),ce(t),null;case 5:oa(t);var s=Xt(Mr.current);if(n=t.type,e!==null&&t.stateNode!=null)hh(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(j(166));return ce(t),null}if(e=Xt(tt.current),ps(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Ze]=t,r[Ar]=i,e=(t.mode&1)!==0,n){case"dialog":B("cancel",r),B("close",r);break;case"iframe":case"object":case"embed":B("load",r);break;case"video":case"audio":for(s=0;s<hr.length;s++)B(hr[s],r);break;case"source":B("error",r);break;case"img":case"image":case"link":B("error",r),B("load",r);break;case"details":B("toggle",r);break;case"input":Va(r,i),B("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},B("invalid",r);break;case"textarea":Qa(r,i),B("invalid",r)}Io(n,i),s=null;for(var o in i)if(i.hasOwnProperty(o)){var l=i[o];o==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&fs(r.textContent,l,e),s=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&fs(r.textContent,l,e),s=["children",""+l]):br.hasOwnProperty(o)&&l!=null&&o==="onScroll"&&B("scroll",r)}switch(n){case"input":is(r),Ka(r,i,!0);break;case"textarea":is(r),Ga(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Gs)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Fc(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[Ze]=t,e[Ar]=r,dh(e,t,!1,!1),t.stateNode=e;e:{switch(o=Ao(n,r),n){case"dialog":B("cancel",e),B("close",e),s=r;break;case"iframe":case"object":case"embed":B("load",e),s=r;break;case"video":case"audio":for(s=0;s<hr.length;s++)B(hr[s],e);s=r;break;case"source":B("error",e),s=r;break;case"img":case"image":case"link":B("error",e),B("load",e),s=r;break;case"details":B("toggle",e),s=r;break;case"input":Va(e,r),s=To(e,r),B("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=Q({},r,{value:void 0}),B("invalid",e);break;case"textarea":Qa(e,r),s=Lo(e,r),B("invalid",e);break;default:s=r}Io(n,s),l=s;for(i in l)if(l.hasOwnProperty(i)){var a=l[i];i==="style"?Wc(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Bc(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Nr(e,a):typeof a=="number"&&Nr(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(br.hasOwnProperty(i)?a!=null&&i==="onScroll"&&B("scroll",e):a!=null&&Dl(e,i,a,o))}switch(n){case"input":is(e),Ka(e,r,!1);break;case"textarea":is(e),Ga(e);break;case"option":r.value!=null&&e.setAttribute("value",""+At(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Cn(e,!!r.multiple,i,!1):r.defaultValue!=null&&Cn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=Gs)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ce(t),null;case 6:if(e&&t.stateNode!=null)fh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(j(166));if(n=Xt(Mr.current),Xt(tt.current),ps(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ze]=t,(i=r.nodeValue!==n)&&(e=Ce,e!==null))switch(e.tag){case 3:fs(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&fs(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ze]=t,t.stateNode=r}return ce(t),null;case 13:if(H(V),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Ne!==null&&t.mode&1&&!(t.flags&128))Rd(),Dn(),t.flags|=98560,i=!1;else if(i=ps(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(j(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(j(317));i[Ze]=t}else Dn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ce(t),i=!1}else We!==null&&(gl(We),We=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||V.current&1?te===0&&(te=3):wa())),t.updateQueue!==null&&(t.flags|=4),ce(t),null);case 4:return Un(),al(e,t),e===null&&$r(t.stateNode.containerInfo),ce(t),null;case 10:return na(t.type._context),ce(t),null;case 17:return je(t.type)&&Js(),ce(t),null;case 19:if(H(V),i=t.memoizedState,i===null)return ce(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)ir(i,!1);else{if(te!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=ri(e),o!==null){for(t.flags|=128,ir(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return F(V,V.current&1|2),t.child}e=e.sibling}i.tail!==null&&X()>Fn&&(t.flags|=128,r=!0,ir(i,!1),t.lanes=4194304)}else{if(!r)if(e=ri(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ir(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!W)return ce(t),null}else 2*X()-i.renderingStartTime>Fn&&n!==1073741824&&(t.flags|=128,r=!0,ir(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=X(),t.sibling=null,n=V.current,F(V,r?n&1|2:n&1),t):(ce(t),null);case 22:case 23:return ya(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?be&1073741824&&(ce(t),t.subtreeFlags&6&&(t.flags|=8192)):ce(t),null;case 24:return null;case 25:return null}throw Error(j(156,t.tag))}function Lm(e,t){switch(Xl(t),t.tag){case 1:return je(t.type)&&Js(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Un(),H(Se),H(he),la(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return oa(t),null;case 13:if(H(V),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(j(340));Dn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return H(V),null;case 4:return Un(),null;case 10:return na(t.type._context),null;case 22:case 23:return ya(),null;case 24:return null;default:return null}}var vs=!1,de=!1,$m=typeof WeakSet=="function"?WeakSet:Set,C=null;function bn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){G(e,t,r)}else n.current=null}function ul(e,t,n){try{n()}catch(r){G(e,t,r)}}var Mu=!1;function Im(e,t){if(Vo=Vs,e=yd(),Jl(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,l=-1,a=-1,u=0,d=0,h=e,f=null;t:for(;;){for(var m;h!==n||s!==0&&h.nodeType!==3||(l=o+s),h!==i||r!==0&&h.nodeType!==3||(a=o+r),h.nodeType===3&&(o+=h.nodeValue.length),(m=h.firstChild)!==null;)f=h,h=m;for(;;){if(h===e)break t;if(f===n&&++u===s&&(l=o),f===i&&++d===r&&(a=o),(m=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=m}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ko={focusedElem:e,selectionRange:n},Vs=!1,C=t;C!==null;)if(t=C,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,C=e;else for(;C!==null;){t=C;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var w=y.memoizedProps,k=y.memoizedState,g=t.stateNode,p=g.getSnapshotBeforeUpdate(t.elementType===t.type?w:Fe(t.type,w),k);g.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(j(163))}}catch(_){G(t,t.return,_)}if(e=t.sibling,e!==null){e.return=t.return,C=e;break}C=t.return}return y=Mu,Mu=!1,y}function xr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&ul(t,n,i)}s=s.next}while(s!==r)}}function ji(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function cl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function ph(e){var t=e.alternate;t!==null&&(e.alternate=null,ph(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ze],delete t[Ar],delete t[Jo],delete t[vm],delete t[ym])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function mh(e){return e.tag===5||e.tag===3||e.tag===4}function Uu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||mh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function dl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Gs));else if(r!==4&&(e=e.child,e!==null))for(dl(e,t,n),e=e.sibling;e!==null;)dl(e,t,n),e=e.sibling}function hl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(hl(e,t,n),e=e.sibling;e!==null;)hl(e,t,n),e=e.sibling}var oe=null,Be=!1;function vt(e,t,n){for(n=n.child;n!==null;)gh(e,t,n),n=n.sibling}function gh(e,t,n){if(et&&typeof et.onCommitFiberUnmount=="function")try{et.onCommitFiberUnmount(gi,n)}catch{}switch(n.tag){case 5:de||bn(n,t);case 6:var r=oe,s=Be;oe=null,vt(e,t,n),oe=r,Be=s,oe!==null&&(Be?(e=oe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):oe.removeChild(n.stateNode));break;case 18:oe!==null&&(Be?(e=oe,n=n.stateNode,e.nodeType===8?oo(e.parentNode,n):e.nodeType===1&&oo(e,n),Rr(e)):oo(oe,n.stateNode));break;case 4:r=oe,s=Be,oe=n.stateNode.containerInfo,Be=!0,vt(e,t,n),oe=r,Be=s;break;case 0:case 11:case 14:case 15:if(!de&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&ul(n,t,o),s=s.next}while(s!==r)}vt(e,t,n);break;case 1:if(!de&&(bn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){G(n,t,l)}vt(e,t,n);break;case 21:vt(e,t,n);break;case 22:n.mode&1?(de=(r=de)||n.memoizedState!==null,vt(e,t,n),de=r):vt(e,t,n);break;default:vt(e,t,n)}}function zu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new $m),t.forEach(function(r){var s=Wm.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function Ue(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,o=t,l=o;e:for(;l!==null;){switch(l.tag){case 5:oe=l.stateNode,Be=!1;break e;case 3:oe=l.stateNode.containerInfo,Be=!0;break e;case 4:oe=l.stateNode.containerInfo,Be=!0;break e}l=l.return}if(oe===null)throw Error(j(160));gh(i,o,s),oe=null,Be=!1;var a=s.alternate;a!==null&&(a.return=null),s.return=null}catch(u){G(s,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)vh(t,e),t=t.sibling}function vh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ue(t,e),Ye(e),r&4){try{xr(3,e,e.return),ji(3,e)}catch(w){G(e,e.return,w)}try{xr(5,e,e.return)}catch(w){G(e,e.return,w)}}break;case 1:Ue(t,e),Ye(e),r&512&&n!==null&&bn(n,n.return);break;case 5:if(Ue(t,e),Ye(e),r&512&&n!==null&&bn(n,n.return),e.flags&32){var s=e.stateNode;try{Nr(s,"")}catch(w){G(e,e.return,w)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&Uc(s,i),Ao(l,o);var u=Ao(l,i);for(o=0;o<a.length;o+=2){var d=a[o],h=a[o+1];d==="style"?Wc(s,h):d==="dangerouslySetInnerHTML"?Bc(s,h):d==="children"?Nr(s,h):Dl(s,d,h,u)}switch(l){case"input":Ro(s,i);break;case"textarea":zc(s,i);break;case"select":var f=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var m=i.value;m!=null?Cn(s,!!i.multiple,m,!1):f!==!!i.multiple&&(i.defaultValue!=null?Cn(s,!!i.multiple,i.defaultValue,!0):Cn(s,!!i.multiple,i.multiple?[]:"",!1))}s[Ar]=i}catch(w){G(e,e.return,w)}}break;case 6:if(Ue(t,e),Ye(e),r&4){if(e.stateNode===null)throw Error(j(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(w){G(e,e.return,w)}}break;case 3:if(Ue(t,e),Ye(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Rr(t.containerInfo)}catch(w){G(e,e.return,w)}break;case 4:Ue(t,e),Ye(e);break;case 13:Ue(t,e),Ye(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(ga=X())),r&4&&zu(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(de=(u=de)||d,Ue(t,e),de=u):Ue(t,e),Ye(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(C=e,d=e.child;d!==null;){for(h=C=d;C!==null;){switch(f=C,m=f.child,f.tag){case 0:case 11:case 14:case 15:xr(4,f,f.return);break;case 1:bn(f,f.return);var y=f.stateNode;if(typeof y.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(w){G(r,n,w)}}break;case 5:bn(f,f.return);break;case 22:if(f.memoizedState!==null){Bu(h);continue}}m!==null?(m.return=f,C=m):Bu(h)}d=d.sibling}e:for(d=null,h=e;;){if(h.tag===5){if(d===null){d=h;try{s=h.stateNode,u?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=h.stateNode,a=h.memoizedProps.style,o=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Hc("display",o))}catch(w){G(e,e.return,w)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(w){G(e,e.return,w)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:Ue(t,e),Ye(e),r&4&&zu(e);break;case 21:break;default:Ue(t,e),Ye(e)}}function Ye(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(mh(n)){var r=n;break e}n=n.return}throw Error(j(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(Nr(s,""),r.flags&=-33);var i=Uu(e);hl(e,i,s);break;case 3:case 4:var o=r.stateNode.containerInfo,l=Uu(e);dl(e,l,o);break;default:throw Error(j(161))}}catch(a){G(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Am(e,t,n){C=e,yh(e)}function yh(e,t,n){for(var r=(e.mode&1)!==0;C!==null;){var s=C,i=s.child;if(s.tag===22&&r){var o=s.memoizedState!==null||vs;if(!o){var l=s.alternate,a=l!==null&&l.memoizedState!==null||de;l=vs;var u=de;if(vs=o,(de=a)&&!u)for(C=s;C!==null;)o=C,a=o.child,o.tag===22&&o.memoizedState!==null?Hu(s):a!==null?(a.return=o,C=a):Hu(s);for(;i!==null;)C=i,yh(i),i=i.sibling;C=s,vs=l,de=u}Fu(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,C=i):Fu(e)}}function Fu(e){for(;C!==null;){var t=C;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:de||ji(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!de)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:Fe(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Eu(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Eu(t,o,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&Rr(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(j(163))}de||t.flags&512&&cl(t)}catch(f){G(t,t.return,f)}}if(t===e){C=null;break}if(n=t.sibling,n!==null){n.return=t.return,C=n;break}C=t.return}}function Bu(e){for(;C!==null;){var t=C;if(t===e){C=null;break}var n=t.sibling;if(n!==null){n.return=t.return,C=n;break}C=t.return}}function Hu(e){for(;C!==null;){var t=C;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ji(4,t)}catch(a){G(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(a){G(t,s,a)}}var i=t.return;try{cl(t)}catch(a){G(t,i,a)}break;case 5:var o=t.return;try{cl(t)}catch(a){G(t,o,a)}}}catch(a){G(t,t.return,a)}if(t===e){C=null;break}var l=t.sibling;if(l!==null){l.return=t.return,C=l;break}C=t.return}}var Dm=Math.ceil,oi=gt.ReactCurrentDispatcher,pa=gt.ReactCurrentOwner,Ie=gt.ReactCurrentBatchConfig,M=0,ie=null,Z=null,le=0,be=0,Nn=Ut(0),te=0,Br=null,sn=0,Ei=0,ma=0,_r=null,xe=null,ga=0,Fn=1/0,rt=null,li=!1,fl=null,Lt=null,ys=!1,bt=null,ai=0,kr=0,pl=null,Is=-1,As=0;function ge(){return M&6?X():Is!==-1?Is:Is=X()}function $t(e){return e.mode&1?M&2&&le!==0?le&-le:xm.transition!==null?(As===0&&(As=nd()),As):(e=U,e!==0||(e=window.event,e=e===void 0?16:ud(e.type)),e):1}function Ve(e,t,n,r){if(50<kr)throw kr=0,pl=null,Error(j(185));Vr(e,n,r),(!(M&2)||e!==ie)&&(e===ie&&(!(M&2)&&(Ei|=n),te===4&&St(e,le)),Ee(e,r),n===1&&M===0&&!(t.mode&1)&&(Fn=X()+500,_i&&zt()))}function Ee(e,t){var n=e.callbackNode;xp(e,t);var r=qs(e,e===ie?le:0);if(r===0)n!==null&&Xa(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Xa(n),t===1)e.tag===0?wm(Wu.bind(null,e)):Cd(Wu.bind(null,e)),mm(function(){!(M&6)&&zt()}),n=null;else{switch(rd(r)){case 1:n=Bl;break;case 4:n=ed;break;case 16:n=Ws;break;case 536870912:n=td;break;default:n=Ws}n=bh(n,wh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function wh(e,t){if(Is=-1,As=0,M&6)throw Error(j(327));var n=e.callbackNode;if(Ln()&&e.callbackNode!==n)return null;var r=qs(e,e===ie?le:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ui(e,r);else{t=r;var s=M;M|=2;var i=_h();(ie!==e||le!==t)&&(rt=null,Fn=X()+500,Zt(e,t));do try{zm();break}catch(l){xh(e,l)}while(!0);ta(),oi.current=i,M=s,Z!==null?t=0:(ie=null,le=0,t=te)}if(t!==0){if(t===2&&(s=Fo(e),s!==0&&(r=s,t=ml(e,s))),t===1)throw n=Br,Zt(e,0),St(e,r),Ee(e,X()),n;if(t===6)St(e,r);else{if(s=e.current.alternate,!(r&30)&&!Mm(s)&&(t=ui(e,r),t===2&&(i=Fo(e),i!==0&&(r=i,t=ml(e,i))),t===1))throw n=Br,Zt(e,0),St(e,r),Ee(e,X()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(j(345));case 2:Kt(e,xe,rt);break;case 3:if(St(e,r),(r&130023424)===r&&(t=ga+500-X(),10<t)){if(qs(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){ge(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=Go(Kt.bind(null,e,xe,rt),t);break}Kt(e,xe,rt);break;case 4:if(St(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var o=31-qe(r);i=1<<o,o=t[o],o>s&&(s=o),r&=~i}if(r=s,r=X()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Dm(r/1960))-r,10<r){e.timeoutHandle=Go(Kt.bind(null,e,xe,rt),r);break}Kt(e,xe,rt);break;case 5:Kt(e,xe,rt);break;default:throw Error(j(329))}}}return Ee(e,X()),e.callbackNode===n?wh.bind(null,e):null}function ml(e,t){var n=_r;return e.current.memoizedState.isDehydrated&&(Zt(e,t).flags|=256),e=ui(e,t),e!==2&&(t=xe,xe=n,t!==null&&gl(t)),e}function gl(e){xe===null?xe=e:xe.push.apply(xe,e)}function Mm(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!Ke(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function St(e,t){for(t&=~ma,t&=~Ei,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-qe(t),r=1<<n;e[n]=-1,t&=~r}}function Wu(e){if(M&6)throw Error(j(327));Ln();var t=qs(e,0);if(!(t&1))return Ee(e,X()),null;var n=ui(e,t);if(e.tag!==0&&n===2){var r=Fo(e);r!==0&&(t=r,n=ml(e,r))}if(n===1)throw n=Br,Zt(e,0),St(e,t),Ee(e,X()),n;if(n===6)throw Error(j(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Kt(e,xe,rt),Ee(e,X()),null}function va(e,t){var n=M;M|=1;try{return e(t)}finally{M=n,M===0&&(Fn=X()+500,_i&&zt())}}function on(e){bt!==null&&bt.tag===0&&!(M&6)&&Ln();var t=M;M|=1;var n=Ie.transition,r=U;try{if(Ie.transition=null,U=1,e)return e()}finally{U=r,Ie.transition=n,M=t,!(M&6)&&zt()}}function ya(){be=Nn.current,H(Nn)}function Zt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,pm(n)),Z!==null)for(n=Z.return;n!==null;){var r=n;switch(Xl(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Js();break;case 3:Un(),H(Se),H(he),la();break;case 5:oa(r);break;case 4:Un();break;case 13:H(V);break;case 19:H(V);break;case 10:na(r.type._context);break;case 22:case 23:ya()}n=n.return}if(ie=e,Z=e=It(e.current,null),le=be=t,te=0,Br=null,ma=Ei=sn=0,xe=_r=null,Yt!==null){for(t=0;t<Yt.length;t++)if(n=Yt[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=s,r.next=o}n.pending=r}Yt=null}return e}function xh(e,t){do{var n=Z;try{if(ta(),Os.current=ii,si){for(var r=K.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}si=!1}if(rn=0,se=ee=K=null,wr=!1,Ur=0,pa.current=null,n===null||n.return===null){te=1,Br=t,Z=null;break}e:{var i=e,o=n.return,l=n,a=t;if(t=le,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,d=l,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=Ru(o);if(m!==null){m.flags&=-257,Ou(m,o,l,i,t),m.mode&1&&Tu(i,u,t),t=m,a=u;var y=t.updateQueue;if(y===null){var w=new Set;w.add(a),t.updateQueue=w}else y.add(a);break e}else{if(!(t&1)){Tu(i,u,t),wa();break e}a=Error(j(426))}}else if(W&&l.mode&1){var k=Ru(o);if(k!==null){!(k.flags&65536)&&(k.flags|=256),Ou(k,o,l,i,t),Zl(zn(a,l));break e}}i=a=zn(a,l),te!==4&&(te=2),_r===null?_r=[i]:_r.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var g=rh(i,a,t);ju(i,g);break e;case 1:l=a;var p=i.type,v=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(Lt===null||!Lt.has(v)))){i.flags|=65536,t&=-t,i.lanes|=t;var _=sh(i,l,t);ju(i,_);break e}}i=i.return}while(i!==null)}Sh(n)}catch(S){t=S,Z===n&&n!==null&&(Z=n=n.return);continue}break}while(!0)}function _h(){var e=oi.current;return oi.current=ii,e===null?ii:e}function wa(){(te===0||te===3||te===2)&&(te=4),ie===null||!(sn&268435455)&&!(Ei&268435455)||St(ie,le)}function ui(e,t){var n=M;M|=2;var r=_h();(ie!==e||le!==t)&&(rt=null,Zt(e,t));do try{Um();break}catch(s){xh(e,s)}while(!0);if(ta(),M=n,oi.current=r,Z!==null)throw Error(j(261));return ie=null,le=0,te}function Um(){for(;Z!==null;)kh(Z)}function zm(){for(;Z!==null&&!dp();)kh(Z)}function kh(e){var t=Eh(e.alternate,e,be);e.memoizedProps=e.pendingProps,t===null?Sh(e):Z=t,pa.current=null}function Sh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Lm(n,t),n!==null){n.flags&=32767,Z=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{te=6,Z=null;return}}else if(n=Om(n,t,be),n!==null){Z=n;return}if(t=t.sibling,t!==null){Z=t;return}Z=t=e}while(t!==null);te===0&&(te=5)}function Kt(e,t,n){var r=U,s=Ie.transition;try{Ie.transition=null,U=1,Fm(e,t,n,r)}finally{Ie.transition=s,U=r}return null}function Fm(e,t,n,r){do Ln();while(bt!==null);if(M&6)throw Error(j(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(j(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(_p(e,i),e===ie&&(Z=ie=null,le=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ys||(ys=!0,bh(Ws,function(){return Ln(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ie.transition,Ie.transition=null;var o=U;U=1;var l=M;M|=4,pa.current=null,Im(e,n),vh(n,e),lm(Ko),Vs=!!Vo,Ko=Vo=null,e.current=n,Am(n),hp(),M=l,U=o,Ie.transition=i}else e.current=n;if(ys&&(ys=!1,bt=e,ai=s),i=e.pendingLanes,i===0&&(Lt=null),mp(n.stateNode),Ee(e,X()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(li)throw li=!1,e=fl,fl=null,e;return ai&1&&e.tag!==0&&Ln(),i=e.pendingLanes,i&1?e===pl?kr++:(kr=0,pl=e):kr=0,zt(),null}function Ln(){if(bt!==null){var e=rd(ai),t=Ie.transition,n=U;try{if(Ie.transition=null,U=16>e?16:e,bt===null)var r=!1;else{if(e=bt,bt=null,ai=0,M&6)throw Error(j(331));var s=M;for(M|=4,C=e.current;C!==null;){var i=C,o=i.child;if(C.flags&16){var l=i.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(C=u;C!==null;){var d=C;switch(d.tag){case 0:case 11:case 15:xr(8,d,i)}var h=d.child;if(h!==null)h.return=d,C=h;else for(;C!==null;){d=C;var f=d.sibling,m=d.return;if(ph(d),d===u){C=null;break}if(f!==null){f.return=m,C=f;break}C=m}}}var y=i.alternate;if(y!==null){var w=y.child;if(w!==null){y.child=null;do{var k=w.sibling;w.sibling=null,w=k}while(w!==null)}}C=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,C=o;else e:for(;C!==null;){if(i=C,i.flags&2048)switch(i.tag){case 0:case 11:case 15:xr(9,i,i.return)}var g=i.sibling;if(g!==null){g.return=i.return,C=g;break e}C=i.return}}var p=e.current;for(C=p;C!==null;){o=C;var v=o.child;if(o.subtreeFlags&2064&&v!==null)v.return=o,C=v;else e:for(o=p;C!==null;){if(l=C,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:ji(9,l)}}catch(S){G(l,l.return,S)}if(l===o){C=null;break e}var _=l.sibling;if(_!==null){_.return=l.return,C=_;break e}C=l.return}}if(M=s,zt(),et&&typeof et.onPostCommitFiberRoot=="function")try{et.onPostCommitFiberRoot(gi,e)}catch{}r=!0}return r}finally{U=n,Ie.transition=t}}return!1}function qu(e,t,n){t=zn(n,t),t=rh(e,t,1),e=Ot(e,t,1),t=ge(),e!==null&&(Vr(e,1,t),Ee(e,t))}function G(e,t,n){if(e.tag===3)qu(e,e,n);else for(;t!==null;){if(t.tag===3){qu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Lt===null||!Lt.has(r))){e=zn(n,e),e=sh(t,e,1),t=Ot(t,e,1),e=ge(),t!==null&&(Vr(t,1,e),Ee(t,e));break}}t=t.return}}function Bm(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ge(),e.pingedLanes|=e.suspendedLanes&n,ie===e&&(le&n)===n&&(te===4||te===3&&(le&130023424)===le&&500>X()-ga?Zt(e,0):ma|=n),Ee(e,t)}function jh(e,t){t===0&&(e.mode&1?(t=as,as<<=1,!(as&130023424)&&(as=4194304)):t=1);var n=ge();e=ft(e,t),e!==null&&(Vr(e,t,n),Ee(e,n))}function Hm(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),jh(e,n)}function Wm(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(j(314))}r!==null&&r.delete(t),jh(e,n)}var Eh;Eh=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Se.current)ke=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ke=!1,Rm(e,t,n);ke=!!(e.flags&131072)}else ke=!1,W&&t.flags&1048576&&Pd(t,Zs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;$s(e,t),e=t.pendingProps;var s=An(t,he.current);On(t,n),s=ua(null,t,r,e,s,n);var i=ca();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,je(r)?(i=!0,Ys(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,sa(t),s.updater=Si,t.stateNode=s,s._reactInternals=t,nl(t,r,e,n),t=il(null,t,r,!0,i,n)):(t.tag=0,W&&i&&Yl(t),me(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch($s(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=Vm(r),e=Fe(r,e),s){case 0:t=sl(null,t,r,e,n);break e;case 1:t=Iu(null,t,r,e,n);break e;case 11:t=Lu(null,t,r,e,n);break e;case 14:t=$u(null,t,r,Fe(r.type,e),n);break e}throw Error(j(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Fe(r,s),sl(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Fe(r,s),Iu(e,t,r,s,n);case 3:e:{if(ah(t),e===null)throw Error(j(387));r=t.pendingProps,i=t.memoizedState,s=i.element,Id(e,t),ni(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=zn(Error(j(423)),t),t=Au(e,t,r,n,s);break e}else if(r!==s){s=zn(Error(j(424)),t),t=Au(e,t,r,n,s);break e}else for(Ne=Rt(t.stateNode.containerInfo.firstChild),Ce=t,W=!0,We=null,n=Ld(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Dn(),r===s){t=pt(e,t,n);break e}me(e,t,r,n)}t=t.child}return t;case 5:return Ad(t),e===null&&Zo(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,o=s.children,Qo(r,s)?o=null:i!==null&&Qo(r,i)&&(t.flags|=32),lh(e,t),me(e,t,o,n),t.child;case 6:return e===null&&Zo(t),null;case 13:return uh(e,t,n);case 4:return ia(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Mn(t,null,r,n):me(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Fe(r,s),Lu(e,t,r,s,n);case 7:return me(e,t,t.pendingProps,n),t.child;case 8:return me(e,t,t.pendingProps.children,n),t.child;case 12:return me(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,o=s.value,F(ei,r._currentValue),r._currentValue=o,i!==null)if(Ke(i.value,o)){if(i.children===s.children&&!Se.current){t=pt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){o=i.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=ut(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?a.next=a:(a.next=d.next,d.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),el(i.return,n,t),l.lanes|=n;break}a=a.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(j(341));o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),el(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}me(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,On(t,n),s=De(s),r=r(s),t.flags|=1,me(e,t,r,n),t.child;case 14:return r=t.type,s=Fe(r,t.pendingProps),s=Fe(r.type,s),$u(e,t,r,s,n);case 15:return ih(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Fe(r,s),$s(e,t),t.tag=1,je(r)?(e=!0,Ys(t)):e=!1,On(t,n),nh(t,r,s),nl(t,r,s,n),il(null,t,r,!0,e,n);case 19:return ch(e,t,n);case 22:return oh(e,t,n)}throw Error(j(156,t.tag))};function bh(e,t){return Zc(e,t)}function qm(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function $e(e,t,n,r){return new qm(e,t,n,r)}function xa(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Vm(e){if(typeof e=="function")return xa(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ul)return 11;if(e===zl)return 14}return 2}function It(e,t){var n=e.alternate;return n===null?(n=$e(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ds(e,t,n,r,s,i){var o=2;if(r=e,typeof e=="function")xa(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case vn:return en(n.children,s,i,t);case Ml:o=8,s|=8;break;case bo:return e=$e(12,n,t,s|2),e.elementType=bo,e.lanes=i,e;case No:return e=$e(13,n,t,s),e.elementType=No,e.lanes=i,e;case Co:return e=$e(19,n,t,s),e.elementType=Co,e.lanes=i,e;case Ac:return bi(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case $c:o=10;break e;case Ic:o=9;break e;case Ul:o=11;break e;case zl:o=14;break e;case wt:o=16,r=null;break e}throw Error(j(130,e==null?e:typeof e,""))}return t=$e(o,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function en(e,t,n,r){return e=$e(7,e,r,t),e.lanes=n,e}function bi(e,t,n,r){return e=$e(22,e,r,t),e.elementType=Ac,e.lanes=n,e.stateNode={isHidden:!1},e}function mo(e,t,n){return e=$e(6,e,null,t),e.lanes=n,e}function go(e,t,n){return t=$e(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Km(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Gi(0),this.expirationTimes=Gi(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Gi(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function _a(e,t,n,r,s,i,o,l,a){return e=new Km(e,t,n,l,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=$e(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},sa(i),e}function Qm(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:gn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Nh(e){if(!e)return Dt;e=e._reactInternals;e:{if(an(e)!==e||e.tag!==1)throw Error(j(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(je(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(j(171))}if(e.tag===1){var n=e.type;if(je(n))return Nd(e,n,t)}return t}function Ch(e,t,n,r,s,i,o,l,a){return e=_a(n,r,!0,e,s,i,o,l,a),e.context=Nh(null),n=e.current,r=ge(),s=$t(n),i=ut(r,s),i.callback=t??null,Ot(n,i,s),e.current.lanes=s,Vr(e,s,r),Ee(e,r),e}function Ni(e,t,n,r){var s=t.current,i=ge(),o=$t(s);return n=Nh(n),t.context===null?t.context=n:t.pendingContext=n,t=ut(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Ot(s,t,o),e!==null&&(Ve(e,s,o,i),Rs(e,s,o)),o}function ci(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Vu(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ka(e,t){Vu(e,t),(e=e.alternate)&&Vu(e,t)}function Gm(){return null}var Ph=typeof reportError=="function"?reportError:function(e){console.error(e)};function Sa(e){this._internalRoot=e}Ci.prototype.render=Sa.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(j(409));Ni(e,t,null,null)};Ci.prototype.unmount=Sa.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;on(function(){Ni(null,e,null,null)}),t[ht]=null}};function Ci(e){this._internalRoot=e}Ci.prototype.unstable_scheduleHydration=function(e){if(e){var t=od();e={blockedOn:null,target:e,priority:t};for(var n=0;n<kt.length&&t!==0&&t<kt[n].priority;n++);kt.splice(n,0,e),n===0&&ad(e)}};function ja(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Pi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ku(){}function Jm(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var u=ci(o);i.call(u)}}var o=Ch(t,r,e,0,null,!1,!1,"",Ku);return e._reactRootContainer=o,e[ht]=o.current,$r(e.nodeType===8?e.parentNode:e),on(),o}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var l=r;r=function(){var u=ci(a);l.call(u)}}var a=_a(e,0,!1,null,null,!1,!1,"",Ku);return e._reactRootContainer=a,e[ht]=a.current,$r(e.nodeType===8?e.parentNode:e),on(function(){Ni(t,a,n,r)}),a}function Ti(e,t,n,r,s){var i=n._reactRootContainer;if(i){var o=i;if(typeof s=="function"){var l=s;s=function(){var a=ci(o);l.call(a)}}Ni(t,o,e,s)}else o=Jm(n,t,e,s,r);return ci(o)}sd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dr(t.pendingLanes);n!==0&&(Hl(t,n|1),Ee(t,X()),!(M&6)&&(Fn=X()+500,zt()))}break;case 13:on(function(){var r=ft(e,1);if(r!==null){var s=ge();Ve(r,e,1,s)}}),ka(e,1)}};Wl=function(e){if(e.tag===13){var t=ft(e,134217728);if(t!==null){var n=ge();Ve(t,e,134217728,n)}ka(e,134217728)}};id=function(e){if(e.tag===13){var t=$t(e),n=ft(e,t);if(n!==null){var r=ge();Ve(n,e,t,r)}ka(e,t)}};od=function(){return U};ld=function(e,t){var n=U;try{return U=e,t()}finally{U=n}};Mo=function(e,t,n){switch(t){case"input":if(Ro(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=xi(r);if(!s)throw Error(j(90));Mc(r),Ro(r,s)}}}break;case"textarea":zc(e,n);break;case"select":t=n.value,t!=null&&Cn(e,!!n.multiple,t,!1)}};Kc=va;Qc=on;var Ym={usingClientEntryPoint:!1,Events:[Qr,_n,xi,qc,Vc,va]},or={findFiberByHostInstance:Jt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Xm={bundleType:or.bundleType,version:or.version,rendererPackageName:or.rendererPackageName,rendererConfig:or.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:gt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Yc(e),e===null?null:e.stateNode},findFiberByHostInstance:or.findFiberByHostInstance||Gm,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ws=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ws.isDisabled&&ws.supportsFiber)try{gi=ws.inject(Xm),et=ws}catch{}}Te.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ym;Te.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ja(t))throw Error(j(200));return Qm(e,t,null,n)};Te.createRoot=function(e,t){if(!ja(e))throw Error(j(299));var n=!1,r="",s=Ph;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=_a(e,1,!1,null,null,n,!1,r,s),e[ht]=t.current,$r(e.nodeType===8?e.parentNode:e),new Sa(t)};Te.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(j(188)):(e=Object.keys(e).join(","),Error(j(268,e)));return e=Yc(t),e=e===null?null:e.stateNode,e};Te.flushSync=function(e){return on(e)};Te.hydrate=function(e,t,n){if(!Pi(t))throw Error(j(200));return Ti(null,e,t,!0,n)};Te.hydrateRoot=function(e,t,n){if(!ja(e))throw Error(j(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",o=Ph;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Ch(t,null,e,1,n??null,s,!1,i,o),e[ht]=t.current,$r(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new Ci(t)};Te.render=function(e,t,n){if(!Pi(t))throw Error(j(200));return Ti(null,e,t,!1,n)};Te.unmountComponentAtNode=function(e){if(!Pi(e))throw Error(j(40));return e._reactRootContainer?(on(function(){Ti(null,null,e,!1,function(){e._reactRootContainer=null,e[ht]=null})}),!0):!1};Te.unstable_batchedUpdates=va;Te.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Pi(n))throw Error(j(200));if(e==null||e._reactInternals===void 0)throw Error(j(38));return Ti(e,t,n,!1,r)};Te.version="18.3.1-next-f1338f8080-20240426";function Th(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Th)}catch(e){console.error(e)}}Th(),Tc.exports=Te;var Zm=Tc.exports,Rh,Qu=Zm;Rh=Qu.createRoot,Qu.hydrateRoot;var Ea={};Object.defineProperty(Ea,"__esModule",{value:!0});Ea.parse=o0;Ea.serialize=l0;const e0=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t0=/^[\u0021-\u003A\u003C-\u007E]*$/,n0=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r0=/^[\u0020-\u003A\u003D-\u007E]*$/,s0=Object.prototype.toString,i0=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function o0(e,t){const n=new i0,r=e.length;if(r<2)return n;const s=(t==null?void 0:t.decode)||a0;let i=0;do{const o=e.indexOf("=",i);if(o===-1)break;const l=e.indexOf(";",i),a=l===-1?r:l;if(o>a){i=e.lastIndexOf(";",o-1)+1;continue}const u=Gu(e,i,o),d=Ju(e,o,u),h=e.slice(u,d);if(n[h]===void 0){let f=Gu(e,o+1,a),m=Ju(e,a,f);const y=s(e.slice(f,m));n[h]=y}i=a+1}while(i<r);return n}function Gu(e,t,n){do{const r=e.charCodeAt(t);if(r!==32&&r!==9)return t}while(++t<n);return n}function Ju(e,t,n){for(;t>n;){const r=e.charCodeAt(--t);if(r!==32&&r!==9)return t+1}return n}function l0(e,t,n){const r=(n==null?void 0:n.encode)||encodeURIComponent;if(!e0.test(e))throw new TypeError(`argument name is invalid: ${e}`);const s=r(t);if(!t0.test(s))throw new TypeError(`argument val is invalid: ${t}`);let i=e+"="+s;if(!n)return i;if(n.maxAge!==void 0){if(!Number.isInteger(n.maxAge))throw new TypeError(`option maxAge is invalid: ${n.maxAge}`);i+="; Max-Age="+n.maxAge}if(n.domain){if(!n0.test(n.domain))throw new TypeError(`option domain is invalid: ${n.domain}`);i+="; Domain="+n.domain}if(n.path){if(!r0.test(n.path))throw new TypeError(`option path is invalid: ${n.path}`);i+="; Path="+n.path}if(n.expires){if(!u0(n.expires)||!Number.isFinite(n.expires.valueOf()))throw new TypeError(`option expires is invalid: ${n.expires}`);i+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(i+="; HttpOnly"),n.secure&&(i+="; Secure"),n.partitioned&&(i+="; Partitioned"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():void 0){case"low":i+="; Priority=Low";break;case"medium":i+="; Priority=Medium";break;case"high":i+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${n.priority}`)}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"none":i+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${n.sameSite}`)}return i}function a0(e){if(e.indexOf("%")===-1)return e;try{return decodeURIComponent(e)}catch{return e}}function u0(e){return s0.call(e)==="[object Date]"}var Yu="popstate";function c0(e={}){function t(r,s){let{pathname:i,search:o,hash:l}=r.location;return vl("",{pathname:i,search:o,hash:l},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function n(r,s){return typeof s=="string"?s:Hr(s)}return h0(t,n,null,e)}function q(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Qe(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function d0(){return Math.random().toString(36).substring(2,10)}function Xu(e,t){return{usr:e.state,key:e.key,idx:t}}function vl(e,t,n=null,r){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Kn(t):t,state:n,key:t&&t.key||r||d0()}}function Hr({pathname:e="/",search:t="",hash:n=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),n&&n!=="#"&&(e+=n.charAt(0)==="#"?n:"#"+n),e}function Kn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function h0(e,t,n,r={}){let{window:s=document.defaultView,v5Compat:i=!1}=r,o=s.history,l="POP",a=null,u=d();u==null&&(u=0,o.replaceState({...o.state,idx:u},""));function d(){return(o.state||{idx:null}).idx}function h(){l="POP";let k=d(),g=k==null?null:k-u;u=k,a&&a({action:l,location:w.location,delta:g})}function f(k,g){l="PUSH";let p=vl(w.location,k,g);u=d()+1;let v=Xu(p,u),_=w.createHref(p);try{o.pushState(v,"",_)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;s.location.assign(_)}i&&a&&a({action:l,location:w.location,delta:1})}function m(k,g){l="REPLACE";let p=vl(w.location,k,g);u=d();let v=Xu(p,u),_=w.createHref(p);o.replaceState(v,"",_),i&&a&&a({action:l,location:w.location,delta:0})}function y(k){return f0(k)}let w={get action(){return l},get location(){return e(s,o)},listen(k){if(a)throw new Error("A history only accepts one active listener");return s.addEventListener(Yu,h),a=k,()=>{s.removeEventListener(Yu,h),a=null}},createHref(k){return t(s,k)},createURL:y,encodeLocation(k){let g=y(k);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:f,replace:m,go(k){return o.go(k)}};return w}function f0(e,t=!1){let n="http://localhost";typeof window<"u"&&(n=window.location.origin!=="null"?window.location.origin:window.location.href),q(n,"No window.location.(origin|href) available to create URL");let r=typeof e=="string"?e:Hr(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}function Oh(e,t,n="/"){return p0(e,t,n,!1)}function p0(e,t,n,r){let s=typeof t=="string"?Kn(t):t,i=mt(s.pathname||"/",n);if(i==null)return null;let o=Lh(e);m0(o);let l=null;for(let a=0;l==null&&a<o.length;++a){let u=b0(i);l=j0(o[a],u,r)}return l}function Lh(e,t=[],n=[],r=""){let s=(i,o,l)=>{let a={relativePath:l===void 0?i.path||"":l,caseSensitive:i.caseSensitive===!0,childrenIndex:o,route:i};a.relativePath.startsWith("/")&&(q(a.relativePath.startsWith(r),`Absolute route path "${a.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),a.relativePath=a.relativePath.slice(r.length));let u=ct([r,a.relativePath]),d=n.concat(a);i.children&&i.children.length>0&&(q(i.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${u}".`),Lh(i.children,t,d,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:k0(u,i.index),routesMeta:d})};return e.forEach((i,o)=>{var l;if(i.path===""||!((l=i.path)!=null&&l.includes("?")))s(i,o);else for(let a of $h(i.path))s(i,o,a)}),t}function $h(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,s=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return s?[i,""]:[i];let o=$h(r.join("/")),l=[];return l.push(...o.map(a=>a===""?i:[i,a].join("/"))),s&&l.push(...o),l.map(a=>e.startsWith("/")&&a===""?"/":a)}function m0(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:S0(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}var g0=/^:[\w-]+$/,v0=3,y0=2,w0=1,x0=10,_0=-2,Zu=e=>e==="*";function k0(e,t){let n=e.split("/"),r=n.length;return n.some(Zu)&&(r+=_0),t&&(r+=y0),n.filter(s=>!Zu(s)).reduce((s,i)=>s+(g0.test(i)?v0:i===""?w0:x0),r)}function S0(e,t){return e.length===t.length&&e.slice(0,-1).every((r,s)=>r===t[s])?e[e.length-1]-t[t.length-1]:0}function j0(e,t,n=!1){let{routesMeta:r}=e,s={},i="/",o=[];for(let l=0;l<r.length;++l){let a=r[l],u=l===r.length-1,d=i==="/"?t:t.slice(i.length)||"/",h=di({path:a.relativePath,caseSensitive:a.caseSensitive,end:u},d),f=a.route;if(!h&&u&&n&&!r[r.length-1].route.index&&(h=di({path:a.relativePath,caseSensitive:a.caseSensitive,end:!1},d)),!h)return null;Object.assign(s,h.params),o.push({params:s,pathname:ct([i,h.pathname]),pathnameBase:T0(ct([i,h.pathnameBase])),route:f}),h.pathnameBase!=="/"&&(i=ct([i,h.pathnameBase]))}return o}function di(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=E0(e.path,e.caseSensitive,e.end),s=t.match(n);if(!s)return null;let i=s[0],o=i.replace(/(.)\/+$/,"$1"),l=s.slice(1);return{params:r.reduce((u,{paramName:d,isOptional:h},f)=>{if(d==="*"){let y=l[f]||"";o=i.slice(0,i.length-y.length).replace(/(.)\/+$/,"$1")}const m=l[f];return h&&!m?u[d]=void 0:u[d]=(m||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:o,pattern:e}}function E0(e,t=!1,n=!0){Qe(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],s="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,l,a)=>(r.push({paramName:l,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),s+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?s+="\\/*$":e!==""&&e!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,t?void 0:"i"),r]}function b0(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Qe(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function mt(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function N0(e,t="/"){let{pathname:n,search:r="",hash:s=""}=typeof e=="string"?Kn(e):e;return{pathname:n?n.startsWith("/")?n:C0(n,t):t,search:R0(r),hash:O0(s)}}function C0(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(s=>{s===".."?n.length>1&&n.pop():s!=="."&&n.push(s)}),n.length>1?n.join("/"):"/"}function vo(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function P0(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function ba(e){let t=P0(e);return t.map((n,r)=>r===t.length-1?n.pathname:n.pathnameBase)}function Na(e,t,n,r=!1){let s;typeof e=="string"?s=Kn(e):(s={...e},q(!s.pathname||!s.pathname.includes("?"),vo("?","pathname","search",s)),q(!s.pathname||!s.pathname.includes("#"),vo("#","pathname","hash",s)),q(!s.search||!s.search.includes("#"),vo("#","search","hash",s)));let i=e===""||s.pathname==="",o=i?"/":s.pathname,l;if(o==null)l=n;else{let h=t.length-1;if(!r&&o.startsWith("..")){let f=o.split("/");for(;f[0]==="..";)f.shift(),h-=1;s.pathname=f.join("/")}l=h>=0?t[h]:"/"}let a=N0(s,l),u=o&&o!=="/"&&o.endsWith("/"),d=(i||o===".")&&n.endsWith("/");return!a.pathname.endsWith("/")&&(u||d)&&(a.pathname+="/"),a}var ct=e=>e.join("/").replace(/\/\/+/g,"/"),T0=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),R0=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,O0=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function L0(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Ih=["POST","PUT","PATCH","DELETE"];new Set(Ih);var $0=["GET",...Ih];new Set($0);var Qn=x.createContext(null);Qn.displayName="DataRouter";var Ri=x.createContext(null);Ri.displayName="DataRouterState";var Ah=x.createContext({isTransitioning:!1});Ah.displayName="ViewTransition";var I0=x.createContext(new Map);I0.displayName="Fetchers";var A0=x.createContext(null);A0.displayName="Await";var Ge=x.createContext(null);Ge.displayName="Navigation";var Jr=x.createContext(null);Jr.displayName="Location";var Je=x.createContext({outlet:null,matches:[],isDataRoute:!1});Je.displayName="Route";var Ca=x.createContext(null);Ca.displayName="RouteError";function D0(e,{relative:t}={}){q(Gn(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=x.useContext(Ge),{hash:s,pathname:i,search:o}=Yr(e,{relative:t}),l=i;return n!=="/"&&(l=i==="/"?n:ct([n,i])),r.createHref({pathname:l,search:o,hash:s})}function Gn(){return x.useContext(Jr)!=null}function Ft(){return q(Gn(),"useLocation() may be used only in the context of a <Router> component."),x.useContext(Jr).location}var Dh="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Mh(e){x.useContext(Ge).static||x.useLayoutEffect(e)}function Uh(){let{isDataRoute:e}=x.useContext(Je);return e?J0():M0()}function M0(){q(Gn(),"useNavigate() may be used only in the context of a <Router> component.");let e=x.useContext(Qn),{basename:t,navigator:n}=x.useContext(Ge),{matches:r}=x.useContext(Je),{pathname:s}=Ft(),i=JSON.stringify(ba(r)),o=x.useRef(!1);return Mh(()=>{o.current=!0}),x.useCallback((a,u={})=>{if(Qe(o.current,Dh),!o.current)return;if(typeof a=="number"){n.go(a);return}let d=Na(a,JSON.parse(i),s,u.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:ct([t,d.pathname])),(u.replace?n.replace:n.push)(d,u.state,u)},[t,n,i,s,e])}x.createContext(null);function Oi(){let{matches:e}=x.useContext(Je),t=e[e.length-1];return t?t.params:{}}function Yr(e,{relative:t}={}){let{matches:n}=x.useContext(Je),{pathname:r}=Ft(),s=JSON.stringify(ba(n));return x.useMemo(()=>Na(e,JSON.parse(s),r,t==="path"),[e,s,r,t])}function U0(e,t){return zh(e,t)}function zh(e,t,n,r){var g;q(Gn(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:s}=x.useContext(Ge),{matches:i}=x.useContext(Je),o=i[i.length-1],l=o?o.params:{},a=o?o.pathname:"/",u=o?o.pathnameBase:"/",d=o&&o.route;{let p=d&&d.path||"";Fh(a,!d||p.endsWith("*")||p.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${a}" (under <Route path="${p}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${p}"> to <Route path="${p==="/"?"*":`${p}/*`}">.`)}let h=Ft(),f;if(t){let p=typeof t=="string"?Kn(t):t;q(u==="/"||((g=p.pathname)==null?void 0:g.startsWith(u)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${u}" but pathname "${p.pathname}" was given in the \`location\` prop.`),f=p}else f=h;let m=f.pathname||"/",y=m;if(u!=="/"){let p=u.replace(/^\//,"").split("/");y="/"+m.replace(/^\//,"").split("/").slice(p.length).join("/")}let w=Oh(e,{pathname:y});Qe(d||w!=null,`No routes matched location "${f.pathname}${f.search}${f.hash}" `),Qe(w==null||w[w.length-1].route.element!==void 0||w[w.length-1].route.Component!==void 0||w[w.length-1].route.lazy!==void 0,`Matched leaf route at location "${f.pathname}${f.search}${f.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let k=W0(w&&w.map(p=>Object.assign({},p,{params:Object.assign({},l,p.params),pathname:ct([u,s.encodeLocation?s.encodeLocation(p.pathname).pathname:p.pathname]),pathnameBase:p.pathnameBase==="/"?u:ct([u,s.encodeLocation?s.encodeLocation(p.pathnameBase).pathname:p.pathnameBase])})),i,n,r);return t&&k?x.createElement(Jr.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...f},navigationType:"POP"}},k):k}function z0(){let e=G0(),t=L0(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",s={padding:"0.5rem",backgroundColor:r},i={padding:"2px 4px",backgroundColor:r},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=x.createElement(x.Fragment,null,x.createElement("p",null,"💿 Hey developer 👋"),x.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",x.createElement("code",{style:i},"ErrorBoundary")," or"," ",x.createElement("code",{style:i},"errorElement")," prop on your route.")),x.createElement(x.Fragment,null,x.createElement("h2",null,"Unexpected Application Error!"),x.createElement("h3",{style:{fontStyle:"italic"}},t),n?x.createElement("pre",{style:s},n):null,o)}var F0=x.createElement(z0,null),B0=class extends x.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?x.createElement(Je.Provider,{value:this.props.routeContext},x.createElement(Ca.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function H0({routeContext:e,match:t,children:n}){let r=x.useContext(Qn);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),x.createElement(Je.Provider,{value:e},n)}function W0(e,t=[],n=null,r=null){if(e==null){if(!n)return null;if(n.errors)e=n.matches;else if(t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,i=n==null?void 0:n.errors;if(i!=null){let a=s.findIndex(u=>u.route.id&&(i==null?void 0:i[u.route.id])!==void 0);q(a>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(i).join(",")}`),s=s.slice(0,Math.min(s.length,a+1))}let o=!1,l=-1;if(n)for(let a=0;a<s.length;a++){let u=s[a];if((u.route.HydrateFallback||u.route.hydrateFallbackElement)&&(l=a),u.route.id){let{loaderData:d,errors:h}=n,f=u.route.loader&&!d.hasOwnProperty(u.route.id)&&(!h||h[u.route.id]===void 0);if(u.route.lazy||f){o=!0,l>=0?s=s.slice(0,l+1):s=[s[0]];break}}}return s.reduceRight((a,u,d)=>{let h,f=!1,m=null,y=null;n&&(h=i&&u.route.id?i[u.route.id]:void 0,m=u.route.errorElement||F0,o&&(l<0&&d===0?(Fh("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),f=!0,y=null):l===d&&(f=!0,y=u.route.hydrateFallbackElement||null)));let w=t.concat(s.slice(0,d+1)),k=()=>{let g;return h?g=m:f?g=y:u.route.Component?g=x.createElement(u.route.Component,null):u.route.element?g=u.route.element:g=a,x.createElement(H0,{match:u,routeContext:{outlet:a,matches:w,isDataRoute:n!=null},children:g})};return n&&(u.route.ErrorBoundary||u.route.errorElement||d===0)?x.createElement(B0,{location:n.location,revalidation:n.revalidation,component:m,error:h,children:k(),routeContext:{outlet:null,matches:w,isDataRoute:!0}}):k()},null)}function Pa(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function q0(e){let t=x.useContext(Qn);return q(t,Pa(e)),t}function V0(e){let t=x.useContext(Ri);return q(t,Pa(e)),t}function K0(e){let t=x.useContext(Je);return q(t,Pa(e)),t}function Ta(e){let t=K0(e),n=t.matches[t.matches.length-1];return q(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function Q0(){return Ta("useRouteId")}function G0(){var r;let e=x.useContext(Ca),t=V0("useRouteError"),n=Ta("useRouteError");return e!==void 0?e:(r=t.errors)==null?void 0:r[n]}function J0(){let{router:e}=q0("useNavigate"),t=Ta("useNavigate"),n=x.useRef(!1);return Mh(()=>{n.current=!0}),x.useCallback(async(s,i={})=>{Qe(n.current,Dh),n.current&&(typeof s=="number"?e.navigate(s):await e.navigate(s,{fromRouteId:t,...i}))},[e,t])}var ec={};function Fh(e,t,n){!t&&!ec[e]&&(ec[e]=!0,Qe(!1,n))}x.memo(Y0);function Y0({routes:e,future:t,state:n}){return zh(e,void 0,n,t)}function X0({to:e,replace:t,state:n,relative:r}){q(Gn(),"<Navigate> may be used only in the context of a <Router> component.");let{static:s}=x.useContext(Ge);Qe(!s,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:i}=x.useContext(Je),{pathname:o}=Ft(),l=Uh(),a=Na(e,ba(i),o,r==="path"),u=JSON.stringify(a);return x.useEffect(()=>{l(JSON.parse(u),{replace:t,state:n,relative:r})},[l,u,r,t,n]),null}function Ms(e){q(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Z0({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:s,static:i=!1}){q(!Gn(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let o=e.replace(/^\/*/,"/"),l=x.useMemo(()=>({basename:o,navigator:s,static:i,future:{}}),[o,s,i]);typeof n=="string"&&(n=Kn(n));let{pathname:a="/",search:u="",hash:d="",state:h=null,key:f="default"}=n,m=x.useMemo(()=>{let y=mt(a,o);return y==null?null:{location:{pathname:y,search:u,hash:d,state:h,key:f},navigationType:r}},[o,a,u,d,h,f,r]);return Qe(m!=null,`<Router basename="${o}"> is not able to match the URL "${a}${u}${d}" because it does not start with the basename, so the <Router> won't render anything.`),m==null?null:x.createElement(Ge.Provider,{value:l},x.createElement(Jr.Provider,{children:t,value:m}))}function eg({children:e,location:t}){return U0(yl(e),t)}function yl(e,t=[]){let n=[];return x.Children.forEach(e,(r,s)=>{if(!x.isValidElement(r))return;let i=[...t,s];if(r.type===x.Fragment){n.push.apply(n,yl(r.props.children,i));return}q(r.type===Ms,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),q(!r.props.index||!r.props.children,"An index route cannot have child routes.");let o={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=yl(r.props.children,i)),n.push(o)}),n}var Us="get",zs="application/x-www-form-urlencoded";function Li(e){return e!=null&&typeof e.tagName=="string"}function tg(e){return Li(e)&&e.tagName.toLowerCase()==="button"}function ng(e){return Li(e)&&e.tagName.toLowerCase()==="form"}function rg(e){return Li(e)&&e.tagName.toLowerCase()==="input"}function sg(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function ig(e,t){return e.button===0&&(!t||t==="_self")&&!sg(e)}var xs=null;function og(){if(xs===null)try{new FormData(document.createElement("form"),0),xs=!1}catch{xs=!0}return xs}var lg=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function yo(e){return e!=null&&!lg.has(e)?(Qe(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${zs}"`),null):e}function ag(e,t){let n,r,s,i,o;if(ng(e)){let l=e.getAttribute("action");r=l?mt(l,t):null,n=e.getAttribute("method")||Us,s=yo(e.getAttribute("enctype"))||zs,i=new FormData(e)}else if(tg(e)||rg(e)&&(e.type==="submit"||e.type==="image")){let l=e.form;if(l==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let a=e.getAttribute("formaction")||l.getAttribute("action");if(r=a?mt(a,t):null,n=e.getAttribute("formmethod")||l.getAttribute("method")||Us,s=yo(e.getAttribute("formenctype"))||yo(l.getAttribute("enctype"))||zs,i=new FormData(l,e),!og()){let{name:u,type:d,value:h}=e;if(d==="image"){let f=u?`${u}.`:"";i.append(`${f}x`,"0"),i.append(`${f}y`,"0")}else u&&i.append(u,h)}}else{if(Li(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=Us,r=null,s=zs,o=e}return i&&s==="text/plain"&&(o=i,i=void 0),{action:r,method:n.toLowerCase(),encType:s,formData:i,body:o}}function Ra(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function ug(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function cg(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function dg(e,t,n){let r=await Promise.all(e.map(async s=>{let i=t.routes[s.route.id];if(i){let o=await ug(i,n);return o.links?o.links():[]}return[]}));return mg(r.flat(1).filter(cg).filter(s=>s.rel==="stylesheet"||s.rel==="preload").map(s=>s.rel==="stylesheet"?{...s,rel:"prefetch",as:"style"}:{...s,rel:"prefetch"}))}function tc(e,t,n,r,s,i){let o=(a,u)=>n[u]?a.route.id!==n[u].route.id:!0,l=(a,u)=>{var d;return n[u].pathname!==a.pathname||((d=n[u].route.path)==null?void 0:d.endsWith("*"))&&n[u].params["*"]!==a.params["*"]};return i==="assets"?t.filter((a,u)=>o(a,u)||l(a,u)):i==="data"?t.filter((a,u)=>{var h;let d=r.routes[a.route.id];if(!d||!d.hasLoader)return!1;if(o(a,u)||l(a,u))return!0;if(a.route.shouldRevalidate){let f=a.route.shouldRevalidate({currentUrl:new URL(s.pathname+s.search+s.hash,window.origin),currentParams:((h=n[0])==null?void 0:h.params)||{},nextUrl:new URL(e,window.origin),nextParams:a.params,defaultShouldRevalidate:!0});if(typeof f=="boolean")return f}return!0}):[]}function hg(e,t,{includeHydrateFallback:n}={}){return fg(e.map(r=>{let s=t.routes[r.route.id];if(!s)return[];let i=[s.module];return s.clientActionModule&&(i=i.concat(s.clientActionModule)),s.clientLoaderModule&&(i=i.concat(s.clientLoaderModule)),n&&s.hydrateFallbackModule&&(i=i.concat(s.hydrateFallbackModule)),s.imports&&(i=i.concat(s.imports)),i}).flat(1))}function fg(e){return[...new Set(e)]}function pg(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}function mg(e,t){let n=new Set;return new Set(t),e.reduce((r,s)=>{let i=JSON.stringify(pg(s));return n.has(i)||(n.add(i),r.push({key:i,link:s})),r},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var gg=new Set([100,101,204,205]);function vg(e,t){let n=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return n.pathname==="/"?n.pathname="_root.data":t&&mt(n.pathname,t)==="/"?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}function Bh(){let e=x.useContext(Qn);return Ra(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function yg(){let e=x.useContext(Ri);return Ra(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Oa=x.createContext(void 0);Oa.displayName="FrameworkContext";function Hh(){let e=x.useContext(Oa);return Ra(e,"You must render this element inside a <HydratedRouter> element"),e}function wg(e,t){let n=x.useContext(Oa),[r,s]=x.useState(!1),[i,o]=x.useState(!1),{onFocus:l,onBlur:a,onMouseEnter:u,onMouseLeave:d,onTouchStart:h}=t,f=x.useRef(null);x.useEffect(()=>{if(e==="render"&&o(!0),e==="viewport"){let w=g=>{g.forEach(p=>{o(p.isIntersecting)})},k=new IntersectionObserver(w,{threshold:.5});return f.current&&k.observe(f.current),()=>{k.disconnect()}}},[e]),x.useEffect(()=>{if(r){let w=setTimeout(()=>{o(!0)},100);return()=>{clearTimeout(w)}}},[r]);let m=()=>{s(!0)},y=()=>{s(!1),o(!1)};return n?e!=="intent"?[i,f,{}]:[i,f,{onFocus:lr(l,m),onBlur:lr(a,y),onMouseEnter:lr(u,m),onMouseLeave:lr(d,y),onTouchStart:lr(h,m)}]:[!1,f,{}]}function lr(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function xg({page:e,...t}){let{router:n}=Bh(),r=x.useMemo(()=>Oh(n.routes,e,n.basename),[n.routes,e,n.basename]);return r?x.createElement(kg,{page:e,matches:r,...t}):null}function _g(e){let{manifest:t,routeModules:n}=Hh(),[r,s]=x.useState([]);return x.useEffect(()=>{let i=!1;return dg(e,t,n).then(o=>{i||s(o)}),()=>{i=!0}},[e,t,n]),r}function kg({page:e,matches:t,...n}){let r=Ft(),{manifest:s,routeModules:i}=Hh(),{basename:o}=Bh(),{loaderData:l,matches:a}=yg(),u=x.useMemo(()=>tc(e,t,a,s,r,"data"),[e,t,a,s,r]),d=x.useMemo(()=>tc(e,t,a,s,r,"assets"),[e,t,a,s,r]),h=x.useMemo(()=>{if(e===r.pathname+r.search+r.hash)return[];let y=new Set,w=!1;if(t.forEach(g=>{var v;let p=s.routes[g.route.id];!p||!p.hasLoader||(!u.some(_=>_.route.id===g.route.id)&&g.route.id in l&&((v=i[g.route.id])!=null&&v.shouldRevalidate)||p.hasClientLoader?w=!0:y.add(g.route.id))}),y.size===0)return[];let k=vg(e,o);return w&&y.size>0&&k.searchParams.set("_routes",t.filter(g=>y.has(g.route.id)).map(g=>g.route.id).join(",")),[k.pathname+k.search]},[o,l,r,s,u,t,e,i]),f=x.useMemo(()=>hg(d,s),[d,s]),m=_g(d);return x.createElement(x.Fragment,null,h.map(y=>x.createElement("link",{key:y,rel:"prefetch",as:"fetch",href:y,...n})),f.map(y=>x.createElement("link",{key:y,rel:"modulepreload",href:y,...n})),m.map(({key:y,link:w})=>x.createElement("link",{key:y,...w})))}function Sg(...e){return t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})}}var Wh=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Wh&&(window.__reactRouterVersion="7.6.2")}catch{}function jg({basename:e,children:t,window:n}){let r=x.useRef();r.current==null&&(r.current=c0({window:n,v5Compat:!0}));let s=r.current,[i,o]=x.useState({action:s.action,location:s.location}),l=x.useCallback(a=>{x.startTransition(()=>o(a))},[o]);return x.useLayoutEffect(()=>s.listen(l),[s,l]),x.createElement(Z0,{basename:e,children:t,location:i.location,navigationType:i.action,navigator:s})}var qh=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,hi=x.forwardRef(function({onClick:t,discover:n="render",prefetch:r="none",relative:s,reloadDocument:i,replace:o,state:l,target:a,to:u,preventScrollReset:d,viewTransition:h,...f},m){let{basename:y}=x.useContext(Ge),w=typeof u=="string"&&qh.test(u),k,g=!1;if(typeof u=="string"&&w&&(k=u,Wh))try{let D=new URL(window.location.href),R=u.startsWith("//")?new URL(D.protocol+u):new URL(u),N=mt(R.pathname,y);R.origin===D.origin&&N!=null?u=N+R.search+R.hash:g=!0}catch{Qe(!1,`<Link to="${u}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let p=D0(u,{relative:s}),[v,_,S]=wg(r,f),E=Cg(u,{replace:o,state:l,target:a,preventScrollReset:d,relative:s,viewTransition:h});function b(D){t&&t(D),D.defaultPrevented||E(D)}let T=x.createElement("a",{...f,...S,href:k||p,onClick:g||i?t:b,ref:Sg(m,_),target:a,"data-discover":!w&&n==="render"?"true":void 0});return v&&!w?x.createElement(x.Fragment,null,T,x.createElement(xg,{page:p})):T});hi.displayName="Link";var Eg=x.forwardRef(function({"aria-current":t="page",caseSensitive:n=!1,className:r="",end:s=!1,style:i,to:o,viewTransition:l,children:a,...u},d){let h=Yr(o,{relative:u.relative}),f=Ft(),m=x.useContext(Ri),{navigator:y,basename:w}=x.useContext(Ge),k=m!=null&&Lg(h)&&l===!0,g=y.encodeLocation?y.encodeLocation(h).pathname:h.pathname,p=f.pathname,v=m&&m.navigation&&m.navigation.location?m.navigation.location.pathname:null;n||(p=p.toLowerCase(),v=v?v.toLowerCase():null,g=g.toLowerCase()),v&&w&&(v=mt(v,w)||v);const _=g!=="/"&&g.endsWith("/")?g.length-1:g.length;let S=p===g||!s&&p.startsWith(g)&&p.charAt(_)==="/",E=v!=null&&(v===g||!s&&v.startsWith(g)&&v.charAt(g.length)==="/"),b={isActive:S,isPending:E,isTransitioning:k},T=S?t:void 0,D;typeof r=="function"?D=r(b):D=[r,S?"active":null,E?"pending":null,k?"transitioning":null].filter(Boolean).join(" ");let R=typeof i=="function"?i(b):i;return x.createElement(hi,{...u,"aria-current":T,className:D,ref:d,style:R,to:o,viewTransition:l},typeof a=="function"?a(b):a)});Eg.displayName="NavLink";var bg=x.forwardRef(({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:s,state:i,method:o=Us,action:l,onSubmit:a,relative:u,preventScrollReset:d,viewTransition:h,...f},m)=>{let y=Rg(),w=Og(l,{relative:u}),k=o.toLowerCase()==="get"?"get":"post",g=typeof l=="string"&&qh.test(l),p=v=>{if(a&&a(v),v.defaultPrevented)return;v.preventDefault();let _=v.nativeEvent.submitter,S=(_==null?void 0:_.getAttribute("formmethod"))||o;y(_||v.currentTarget,{fetcherKey:t,method:S,navigate:n,replace:s,state:i,relative:u,preventScrollReset:d,viewTransition:h})};return x.createElement("form",{ref:m,method:k,action:w,onSubmit:r?a:p,...f,"data-discover":!g&&e==="render"?"true":void 0})});bg.displayName="Form";function Ng(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Vh(e){let t=x.useContext(Qn);return q(t,Ng(e)),t}function Cg(e,{target:t,replace:n,state:r,preventScrollReset:s,relative:i,viewTransition:o}={}){let l=Uh(),a=Ft(),u=Yr(e,{relative:i});return x.useCallback(d=>{if(ig(d,t)){d.preventDefault();let h=n!==void 0?n:Hr(a)===Hr(u);l(e,{replace:h,state:r,preventScrollReset:s,relative:i,viewTransition:o})}},[a,l,u,n,r,t,e,s,i,o])}var Pg=0,Tg=()=>`__${String(++Pg)}__`;function Rg(){let{router:e}=Vh("useSubmit"),{basename:t}=x.useContext(Ge),n=Q0();return x.useCallback(async(r,s={})=>{let{action:i,method:o,encType:l,formData:a,body:u}=ag(r,t);if(s.navigate===!1){let d=s.fetcherKey||Tg();await e.fetch(d,n,s.action||i,{preventScrollReset:s.preventScrollReset,formData:a,body:u,formMethod:s.method||o,formEncType:s.encType||l,flushSync:s.flushSync})}else await e.navigate(s.action||i,{preventScrollReset:s.preventScrollReset,formData:a,body:u,formMethod:s.method||o,formEncType:s.encType||l,replace:s.replace,state:s.state,fromRouteId:n,flushSync:s.flushSync,viewTransition:s.viewTransition})},[e,t,n])}function Og(e,{relative:t}={}){let{basename:n}=x.useContext(Ge),r=x.useContext(Je);q(r,"useFormAction must be used inside a RouteContext");let[s]=r.matches.slice(-1),i={...Yr(e||".",{relative:t})},o=Ft();if(e==null){i.search=o.search;let l=new URLSearchParams(i.search),a=l.getAll("index");if(a.some(d=>d==="")){l.delete("index"),a.filter(h=>h).forEach(h=>l.append("index",h));let d=l.toString();i.search=d?`?${d}`:""}}return(!e||e===".")&&s.route.index&&(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(i.pathname=i.pathname==="/"?n:ct([n,i.pathname])),Hr(i)}function Lg(e,t={}){let n=x.useContext(Ah);q(n!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=Vh("useViewTransitionState"),s=Yr(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=mt(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=mt(n.nextLocation.pathname,r)||n.nextLocation.pathname;return di(s.pathname,o)!=null||di(s.pathname,i)!=null}[...gg];/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var $g={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ig=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),J=(e,t)=>{const n=x.forwardRef(({color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:l="",children:a,...u},d)=>x.createElement("svg",{ref:d,...$g,width:s,height:s,stroke:r,strokeWidth:o?Number(i)*24/Number(s):i,className:["lucide",`lucide-${Ig(e)}`,l].join(" "),...u},[...t.map(([h,f])=>x.createElement(h,f)),...Array.isArray(a)?a:[a]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ag=J("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kh=J("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bn=J("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wl=J("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qh=J("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gh=J("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dg=J("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mg=J("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ug=J("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xl=J("Loader",[["line",{x1:"12",x2:"12",y1:"2",y2:"6",key:"gza1u7"}],["line",{x1:"12",x2:"12",y1:"18",y2:"22",key:"1qhbu9"}],["line",{x1:"4.93",x2:"7.76",y1:"4.93",y2:"7.76",key:"xae44r"}],["line",{x1:"16.24",x2:"19.07",y1:"16.24",y2:"19.07",key:"bxnmvf"}],["line",{x1:"2",x2:"6",y1:"12",y2:"12",key:"89khin"}],["line",{x1:"18",x2:"22",y1:"12",y2:"12",key:"pb8tfm"}],["line",{x1:"4.93",x2:"7.76",y1:"19.07",y2:"16.24",key:"1uxjnu"}],["line",{x1:"16.24",x2:"19.07",y1:"7.76",y2:"4.93",key:"6duxfx"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zg=J("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fg=J("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _l=J("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jh=J("Megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yh=J("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xh=J("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bg=J("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hg=J("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wg=J("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qg=J("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vg=J("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const La=J("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Kg="modulepreload",Qg=function(e){return"/"+e},nc={},Xr=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),l=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));s=Promise.allSettled(n.map(a=>{if(a=Qg(a),a in nc)return;nc[a]=!0;const u=a.endsWith(".css"),d=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${d}`))return;const h=document.createElement("link");if(h.rel=u?"stylesheet":Kg,u||(h.as="script"),h.crossOrigin="",h.href=a,l&&h.setAttribute("nonce",l),document.head.appendChild(h),u)return new Promise((f,m)=>{h.addEventListener("load",f),h.addEventListener("error",()=>m(new Error(`Unable to preload CSS for ${a}`)))})}))}function i(o){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=o,window.dispatchEvent(l),!l.defaultPrevented)throw o}return s.then(o=>{for(const l of o||[])l.status==="rejected"&&i(l.reason);return t().catch(i)})},Gg=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>Xr(async()=>{const{default:r}=await Promise.resolve().then(()=>Jn);return{default:r}},[]).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)};class $a extends Error{constructor(t,n="FunctionsError",r){super(t),this.name=n,this.context=r}}class Jg extends $a{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class Yg extends $a{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class Xg extends $a{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var kl;(function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"})(kl||(kl={}));var Zg=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{u(r.next(d))}catch(h){o(h)}}function a(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};class ev{constructor(t,{headers:n={},customFetch:r,region:s=kl.Any}={}){this.url=t,this.headers=n,this.region=s,this.fetch=Gg(r)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,n={}){var r;return Zg(this,void 0,void 0,function*(){try{const{headers:s,method:i,body:o}=n;let l={},{region:a}=n;a||(a=this.region),a&&a!=="any"&&(l["x-region"]=a);let u;o&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&(typeof Blob<"u"&&o instanceof Blob||o instanceof ArrayBuffer?(l["Content-Type"]="application/octet-stream",u=o):typeof o=="string"?(l["Content-Type"]="text/plain",u=o):typeof FormData<"u"&&o instanceof FormData?u=o:(l["Content-Type"]="application/json",u=JSON.stringify(o)));const d=yield this.fetch(`${this.url}/${t}`,{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},l),this.headers),s),body:u}).catch(y=>{throw new Jg(y)}),h=d.headers.get("x-relay-error");if(h&&h==="true")throw new Yg(d);if(!d.ok)throw new Xg(d);let f=((r=d.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),m;return f==="application/json"?m=yield d.json():f==="application/octet-stream"?m=yield d.blob():f==="text/event-stream"?m=d:f==="multipart/form-data"?m=yield d.formData():m=yield d.text(),{data:m,error:null}}catch(s){return{data:null,error:s}}})}}var _e={},Ia={},$i={},Zr={},Ii={},Ai={},tv=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},Hn=tv();const nv=Hn.fetch,Zh=Hn.fetch.bind(Hn),ef=Hn.Headers,rv=Hn.Request,sv=Hn.Response,Jn=Object.freeze(Object.defineProperty({__proto__:null,Headers:ef,Request:rv,Response:sv,default:Zh,fetch:nv},Symbol.toStringTag,{value:"Module"})),iv=Nf(Jn);var Di={};Object.defineProperty(Di,"__esModule",{value:!0});let ov=class extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}};Di.default=ov;var tf=Ae&&Ae.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ai,"__esModule",{value:!0});const lv=tf(iv),av=tf(Di);let uv=class{constructor(t){this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=t.headers,this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=t.shouldThrowOnError,this.signal=t.signal,this.isMaybeSingle=t.isMaybeSingle,t.fetch?this.fetch=t.fetch:typeof fetch>"u"?this.fetch=lv.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(t,n){return this.headers=Object.assign({},this.headers),this.headers[t]=n,this}then(t,n){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const r=this.fetch;let s=r(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async i=>{var o,l,a;let u=null,d=null,h=null,f=i.status,m=i.statusText;if(i.ok){if(this.method!=="HEAD"){const g=await i.text();g===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?d=g:d=JSON.parse(g))}const w=(o=this.headers.Prefer)===null||o===void 0?void 0:o.match(/count=(exact|planned|estimated)/),k=(l=i.headers.get("content-range"))===null||l===void 0?void 0:l.split("/");w&&k&&k.length>1&&(h=parseInt(k[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(d)&&(d.length>1?(u={code:"PGRST116",details:`Results contain ${d.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},d=null,h=null,f=406,m="Not Acceptable"):d.length===1?d=d[0]:d=null)}else{const w=await i.text();try{u=JSON.parse(w),Array.isArray(u)&&i.status===404&&(d=[],u=null,f=200,m="OK")}catch{i.status===404&&w===""?(f=204,m="No Content"):u={message:w}}if(u&&this.isMaybeSingle&&(!((a=u==null?void 0:u.details)===null||a===void 0)&&a.includes("0 rows"))&&(u=null,f=200,m="OK"),u&&this.shouldThrowOnError)throw new av.default(u)}return{error:u,data:d,count:h,status:f,statusText:m}});return this.shouldThrowOnError||(s=s.catch(i=>{var o,l,a;return{error:{message:`${(o=i==null?void 0:i.name)!==null&&o!==void 0?o:"FetchError"}: ${i==null?void 0:i.message}`,details:`${(l=i==null?void 0:i.stack)!==null&&l!==void 0?l:""}`,hint:"",code:`${(a=i==null?void 0:i.code)!==null&&a!==void 0?a:""}`},data:null,count:null,status:0,statusText:""}})),s.then(t,n)}returns(){return this}overrideTypes(){return this}};Ai.default=uv;var cv=Ae&&Ae.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ii,"__esModule",{value:!0});const dv=cv(Ai);let hv=class extends dv.default{select(t){let n=!1;const r=(t??"*").split("").map(s=>/\s/.test(s)&&!n?"":(s==='"'&&(n=!n),s)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(t,{ascending:n=!0,nullsFirst:r,foreignTable:s,referencedTable:i=s}={}){const o=i?`${i}.order`:"order",l=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${l?`${l},`:""}${t}.${n?"asc":"desc"}${r===void 0?"":r?".nullsfirst":".nullslast"}`),this}limit(t,{foreignTable:n,referencedTable:r=n}={}){const s=typeof r>"u"?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${t}`),this}range(t,n,{foreignTable:r,referencedTable:s=r}={}){const i=typeof s>"u"?"offset":`${s}.offset`,o=typeof s>"u"?"limit":`${s}.limit`;return this.url.searchParams.set(i,`${t}`),this.url.searchParams.set(o,`${n-t+1}`),this}abortSignal(t){return this.signal=t,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:t=!1,verbose:n=!1,settings:r=!1,buffers:s=!1,wal:i=!1,format:o="text"}={}){var l;const a=[t?"analyze":null,n?"verbose":null,r?"settings":null,s?"buffers":null,i?"wal":null].filter(Boolean).join("|"),u=(l=this.headers.Accept)!==null&&l!==void 0?l:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${u}"; options=${a};`,o==="json"?this:this}rollback(){var t;return((t=this.headers.Prefer)!==null&&t!==void 0?t:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};Ii.default=hv;var fv=Ae&&Ae.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Zr,"__esModule",{value:!0});const pv=fv(Ii);let mv=class extends pv.default{eq(t,n){return this.url.searchParams.append(t,`eq.${n}`),this}neq(t,n){return this.url.searchParams.append(t,`neq.${n}`),this}gt(t,n){return this.url.searchParams.append(t,`gt.${n}`),this}gte(t,n){return this.url.searchParams.append(t,`gte.${n}`),this}lt(t,n){return this.url.searchParams.append(t,`lt.${n}`),this}lte(t,n){return this.url.searchParams.append(t,`lte.${n}`),this}like(t,n){return this.url.searchParams.append(t,`like.${n}`),this}likeAllOf(t,n){return this.url.searchParams.append(t,`like(all).{${n.join(",")}}`),this}likeAnyOf(t,n){return this.url.searchParams.append(t,`like(any).{${n.join(",")}}`),this}ilike(t,n){return this.url.searchParams.append(t,`ilike.${n}`),this}ilikeAllOf(t,n){return this.url.searchParams.append(t,`ilike(all).{${n.join(",")}}`),this}ilikeAnyOf(t,n){return this.url.searchParams.append(t,`ilike(any).{${n.join(",")}}`),this}is(t,n){return this.url.searchParams.append(t,`is.${n}`),this}in(t,n){const r=Array.from(new Set(n)).map(s=>typeof s=="string"&&new RegExp("[,()]").test(s)?`"${s}"`:`${s}`).join(",");return this.url.searchParams.append(t,`in.(${r})`),this}contains(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cs.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cs.{${n.join(",")}}`):this.url.searchParams.append(t,`cs.${JSON.stringify(n)}`),this}containedBy(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cd.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cd.{${n.join(",")}}`):this.url.searchParams.append(t,`cd.${JSON.stringify(n)}`),this}rangeGt(t,n){return this.url.searchParams.append(t,`sr.${n}`),this}rangeGte(t,n){return this.url.searchParams.append(t,`nxl.${n}`),this}rangeLt(t,n){return this.url.searchParams.append(t,`sl.${n}`),this}rangeLte(t,n){return this.url.searchParams.append(t,`nxr.${n}`),this}rangeAdjacent(t,n){return this.url.searchParams.append(t,`adj.${n}`),this}overlaps(t,n){return typeof n=="string"?this.url.searchParams.append(t,`ov.${n}`):this.url.searchParams.append(t,`ov.{${n.join(",")}}`),this}textSearch(t,n,{config:r,type:s}={}){let i="";s==="plain"?i="pl":s==="phrase"?i="ph":s==="websearch"&&(i="w");const o=r===void 0?"":`(${r})`;return this.url.searchParams.append(t,`${i}fts${o}.${n}`),this}match(t){return Object.entries(t).forEach(([n,r])=>{this.url.searchParams.append(n,`eq.${r}`)}),this}not(t,n,r){return this.url.searchParams.append(t,`not.${n}.${r}`),this}or(t,{foreignTable:n,referencedTable:r=n}={}){const s=r?`${r}.or`:"or";return this.url.searchParams.append(s,`(${t})`),this}filter(t,n,r){return this.url.searchParams.append(t,`${n}.${r}`),this}};Zr.default=mv;var gv=Ae&&Ae.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty($i,"__esModule",{value:!0});const ar=gv(Zr);let vv=class{constructor(t,{headers:n={},schema:r,fetch:s}){this.url=t,this.headers=n,this.schema=r,this.fetch=s}select(t,{head:n=!1,count:r}={}){const s=n?"HEAD":"GET";let i=!1;const o=(t??"*").split("").map(l=>/\s/.test(l)&&!i?"":(l==='"'&&(i=!i),l)).join("");return this.url.searchParams.set("select",o),r&&(this.headers.Prefer=`count=${r}`),new ar.default({method:s,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(t,{count:n,defaultToNull:r=!0}={}){const s="POST",i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),n&&i.push(`count=${n}`),r||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(t)){const o=t.reduce((l,a)=>l.concat(Object.keys(a)),[]);if(o.length>0){const l=[...new Set(o)].map(a=>`"${a}"`);this.url.searchParams.set("columns",l.join(","))}}return new ar.default({method:s,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}upsert(t,{onConflict:n,ignoreDuplicates:r=!1,count:s,defaultToNull:i=!0}={}){const o="POST",l=[`resolution=${r?"ignore":"merge"}-duplicates`];if(n!==void 0&&this.url.searchParams.set("on_conflict",n),this.headers.Prefer&&l.push(this.headers.Prefer),s&&l.push(`count=${s}`),i||l.push("missing=default"),this.headers.Prefer=l.join(","),Array.isArray(t)){const a=t.reduce((u,d)=>u.concat(Object.keys(d)),[]);if(a.length>0){const u=[...new Set(a)].map(d=>`"${d}"`);this.url.searchParams.set("columns",u.join(","))}}return new ar.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}update(t,{count:n}={}){const r="PATCH",s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),n&&s.push(`count=${n}`),this.headers.Prefer=s.join(","),new ar.default({method:r,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}delete({count:t}={}){const n="DELETE",r=[];return t&&r.push(`count=${t}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new ar.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};$i.default=vv;var Mi={},Ui={};Object.defineProperty(Ui,"__esModule",{value:!0});Ui.version=void 0;Ui.version="0.0.0-automated";Object.defineProperty(Mi,"__esModule",{value:!0});Mi.DEFAULT_HEADERS=void 0;const yv=Ui;Mi.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${yv.version}`};var nf=Ae&&Ae.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ia,"__esModule",{value:!0});const wv=nf($i),xv=nf(Zr),_v=Mi;let kv=class rf{constructor(t,{headers:n={},schema:r,fetch:s}={}){this.url=t,this.headers=Object.assign(Object.assign({},_v.DEFAULT_HEADERS),n),this.schemaName=r,this.fetch=s}from(t){const n=new URL(`${this.url}/${t}`);return new wv.default(n,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new rf(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(t,n={},{head:r=!1,get:s=!1,count:i}={}){let o;const l=new URL(`${this.url}/rpc/${t}`);let a;r||s?(o=r?"HEAD":"GET",Object.entries(n).filter(([d,h])=>h!==void 0).map(([d,h])=>[d,Array.isArray(h)?`{${h.join(",")}}`:`${h}`]).forEach(([d,h])=>{l.searchParams.append(d,h)})):(o="POST",a=n);const u=Object.assign({},this.headers);return i&&(u.Prefer=`count=${i}`),new xv.default({method:o,url:l,headers:u,schema:this.schemaName,body:a,fetch:this.fetch,allowEmpty:!1})}};Ia.default=kv;var Yn=Ae&&Ae.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(_e,"__esModule",{value:!0});_e.PostgrestError=_e.PostgrestBuilder=_e.PostgrestTransformBuilder=_e.PostgrestFilterBuilder=_e.PostgrestQueryBuilder=_e.PostgrestClient=void 0;const sf=Yn(Ia);_e.PostgrestClient=sf.default;const of=Yn($i);_e.PostgrestQueryBuilder=of.default;const lf=Yn(Zr);_e.PostgrestFilterBuilder=lf.default;const af=Yn(Ii);_e.PostgrestTransformBuilder=af.default;const uf=Yn(Ai);_e.PostgrestBuilder=uf.default;const cf=Yn(Di);_e.PostgrestError=cf.default;var Sv=_e.default={PostgrestClient:sf.default,PostgrestQueryBuilder:of.default,PostgrestFilterBuilder:lf.default,PostgrestTransformBuilder:af.default,PostgrestBuilder:uf.default,PostgrestError:cf.default};const{PostgrestClient:jv,PostgrestQueryBuilder:Ew,PostgrestFilterBuilder:bw,PostgrestTransformBuilder:Nw,PostgrestBuilder:Cw,PostgrestError:Pw}=Sv;let Sl;typeof window>"u"?Sl=require("ws"):Sl=window.WebSocket;const Ev=Sl,bv="2.11.10",Nv={"X-Client-Info":`realtime-js/${bv}`},Cv="1.0.0",df=1e4,Pv=1e3;var $n;(function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"})($n||($n={}));var pe;(function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"})(pe||(pe={}));var He;(function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"})(He||(He={}));var jl;(function(e){e.websocket="websocket"})(jl||(jl={}));var Gt;(function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"})(Gt||(Gt={}));class Tv{constructor(){this.HEADER_LENGTH=1}decode(t,n){return t.constructor===ArrayBuffer?n(this._binaryDecode(t)):n(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){const n=new DataView(t),r=new TextDecoder;return this._decodeBroadcast(t,n,r)}_decodeBroadcast(t,n,r){const s=n.getUint8(1),i=n.getUint8(2);let o=this.HEADER_LENGTH+2;const l=r.decode(t.slice(o,o+s));o=o+s;const a=r.decode(t.slice(o,o+i));o=o+i;const u=JSON.parse(r.decode(t.slice(o,t.byteLength)));return{ref:null,topic:l,event:a,payload:u}}}class hf{constructor(t,n){this.callback=t,this.timerCalc=n,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=n}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var z;(function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"})(z||(z={}));const rc=(e,t,n={})=>{var r;const s=(r=n.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(t).reduce((i,o)=>(i[o]=Rv(o,e,t,s),i),{})},Rv=(e,t,n,r)=>{const s=t.find(l=>l.name===e),i=s==null?void 0:s.type,o=n[e];return i&&!r.includes(i)?ff(i,o):El(o)},ff=(e,t)=>{if(e.charAt(0)==="_"){const n=e.slice(1,e.length);return Iv(t,n)}switch(e){case z.bool:return Ov(t);case z.float4:case z.float8:case z.int2:case z.int4:case z.int8:case z.numeric:case z.oid:return Lv(t);case z.json:case z.jsonb:return $v(t);case z.timestamp:return Av(t);case z.abstime:case z.date:case z.daterange:case z.int4range:case z.int8range:case z.money:case z.reltime:case z.text:case z.time:case z.timestamptz:case z.timetz:case z.tsrange:case z.tstzrange:return El(t);default:return El(t)}},El=e=>e,Ov=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},Lv=e=>{if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},$v=e=>{if(typeof e=="string")try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},Iv=(e,t)=>{if(typeof e!="string")return e;const n=e.length-1,r=e[n];if(e[0]==="{"&&r==="}"){let i;const o=e.slice(1,n);try{i=JSON.parse("["+o+"]")}catch{i=o?o.split(","):[]}return i.map(l=>ff(t,l))}return e},Av=e=>typeof e=="string"?e.replace(" ","T"):e,pf=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class wo{constructor(t,n,r={},s=df){this.channel=t,this.event=n,this.payload=r,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,n){var r;return this._hasReceived(t)&&n((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:t,callback:n}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const t=n=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=n,this._matchReceive(n)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,n){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:n})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:n}){this.recHooks.filter(r=>r.status===t).forEach(r=>r.callback(n))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var sc;(function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"})(sc||(sc={}));class Sr{constructor(t,n){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(n==null?void 0:n.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},s=>{const{onJoin:i,onLeave:o,onSync:l}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Sr.syncState(this.state,s,i,o),this.pendingDiffs.forEach(a=>{this.state=Sr.syncDiff(this.state,a,i,o)}),this.pendingDiffs=[],l()}),this.channel._on(r.diff,{},s=>{const{onJoin:i,onLeave:o,onSync:l}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(s):(this.state=Sr.syncDiff(this.state,s,i,o),l())}),this.onJoin((s,i,o)=>{this.channel._trigger("presence",{event:"join",key:s,currentPresences:i,newPresences:o})}),this.onLeave((s,i,o)=>{this.channel._trigger("presence",{event:"leave",key:s,currentPresences:i,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,n,r,s){const i=this.cloneDeep(t),o=this.transformState(n),l={},a={};return this.map(i,(u,d)=>{o[u]||(a[u]=d)}),this.map(o,(u,d)=>{const h=i[u];if(h){const f=d.map(k=>k.presence_ref),m=h.map(k=>k.presence_ref),y=d.filter(k=>m.indexOf(k.presence_ref)<0),w=h.filter(k=>f.indexOf(k.presence_ref)<0);y.length>0&&(l[u]=y),w.length>0&&(a[u]=w)}else l[u]=d}),this.syncDiff(i,{joins:l,leaves:a},r,s)}static syncDiff(t,n,r,s){const{joins:i,leaves:o}={joins:this.transformState(n.joins),leaves:this.transformState(n.leaves)};return r||(r=()=>{}),s||(s=()=>{}),this.map(i,(l,a)=>{var u;const d=(u=t[l])!==null&&u!==void 0?u:[];if(t[l]=this.cloneDeep(a),d.length>0){const h=t[l].map(m=>m.presence_ref),f=d.filter(m=>h.indexOf(m.presence_ref)<0);t[l].unshift(...f)}r(l,d,a)}),this.map(o,(l,a)=>{let u=t[l];if(!u)return;const d=a.map(h=>h.presence_ref);u=u.filter(h=>d.indexOf(h.presence_ref)<0),t[l]=u,s(l,u,a),u.length===0&&delete t[l]}),t}static map(t,n){return Object.getOwnPropertyNames(t).map(r=>n(r,t[r]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((n,r)=>{const s=t[r];return"metas"in s?n[r]=s.metas.map(i=>(i.presence_ref=i.phx_ref,delete i.phx_ref,delete i.phx_ref_prev,i)):n[r]=s,n},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var ic;(function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"})(ic||(ic={}));var oc;(function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"})(oc||(oc={}));var st;(function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"})(st||(st={}));class Aa{constructor(t,n={config:{}},r){this.topic=t,this.params=n,this.socket=r,this.bindings={},this.state=pe.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},n.config),this.timeout=this.socket.timeout,this.joinPush=new wo(this,He.join,this.params,this.timeout),this.rejoinTimer=new hf(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=pe.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(s=>s.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=pe.closed,this.socket._remove(this)}),this._onError(s=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,s),this.state=pe.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=pe.errored,this.rejoinTimer.scheduleTimeout())}),this._on(He.reply,{},(s,i)=>{this._trigger(this._replyEventName(i),s)}),this.presence=new Sr(this),this.broadcastEndpointURL=pf(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,n=this.timeout){var r,s;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:i,presence:o,private:l}}=this.params;this._onError(d=>t==null?void 0:t(st.CHANNEL_ERROR,d)),this._onClose(()=>t==null?void 0:t(st.CLOSED));const a={},u={broadcast:i,presence:o,postgres_changes:(s=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(d=>d.filter))!==null&&s!==void 0?s:[],private:l};this.socket.accessTokenValue&&(a.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:u},a)),this.joinedOnce=!0,this._rejoin(n),this.joinPush.receive("ok",async({postgres_changes:d})=>{var h;if(this.socket.setAuth(),d===void 0){t==null||t(st.SUBSCRIBED);return}else{const f=this.bindings.postgres_changes,m=(h=f==null?void 0:f.length)!==null&&h!==void 0?h:0,y=[];for(let w=0;w<m;w++){const k=f[w],{filter:{event:g,schema:p,table:v,filter:_}}=k,S=d&&d[w];if(S&&S.event===g&&S.schema===p&&S.table===v&&S.filter===_)y.push(Object.assign(Object.assign({},k),{id:S.id}));else{this.unsubscribe(),this.state=pe.errored,t==null||t(st.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=y,t&&t(st.SUBSCRIBED);return}}).receive("error",d=>{this.state=pe.errored,t==null||t(st.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(d).join(", ")||"error")))}).receive("timeout",()=>{t==null||t(st.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(t,n={}){return await this.send({type:"presence",event:"track",payload:t},n.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,n,r){return this._on(t,n,r)}async send(t,n={}){var r,s;if(!this._canPush()&&t.type==="broadcast"){const{event:i,payload:o}=t,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:o,private:this.private}]})};try{const u=await this._fetchWithTimeout(this.broadcastEndpointURL,a,(r=n.timeout)!==null&&r!==void 0?r:this.timeout);return await((s=u.body)===null||s===void 0?void 0:s.cancel()),u.ok?"ok":"error"}catch(u){return u.name==="AbortError"?"timed out":"error"}}else return new Promise(i=>{var o,l,a;const u=this._push(t.type,t,n.timeout||this.timeout);t.type==="broadcast"&&!(!((a=(l=(o=this.params)===null||o===void 0?void 0:o.config)===null||l===void 0?void 0:l.broadcast)===null||a===void 0)&&a.ack)&&i("ok"),u.receive("ok",()=>i("ok")),u.receive("error",()=>i("error")),u.receive("timeout",()=>i("timed out"))})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=pe.leaving;const n=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(He.close,"leave",this._joinRef())};return this.joinPush.destroy(),new Promise(r=>{const s=new wo(this,He.leave,{},t);s.receive("ok",()=>{n(),r("ok")}).receive("timeout",()=>{n(),r("timed out")}).receive("error",()=>{r("error")}),s.send(),this._canPush()||s.trigger("ok",{})})}teardown(){this.pushBuffer.forEach(t=>t.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(t,n,r){const s=new AbortController,i=setTimeout(()=>s.abort(),r),o=await this.socket.fetch(t,Object.assign(Object.assign({},n),{signal:s.signal}));return clearTimeout(i),o}_push(t,n,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new wo(this,t,n,r);return this._canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}_onMessage(t,n,r){return n}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,n,r){var s,i;const o=t.toLocaleLowerCase(),{close:l,error:a,leave:u,join:d}=He;if(r&&[l,a,u,d].indexOf(o)>=0&&r!==this._joinRef())return;let f=this._onMessage(o,n,r);if(n&&!f)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(s=this.bindings.postgres_changes)===null||s===void 0||s.filter(m=>{var y,w,k;return((y=m.filter)===null||y===void 0?void 0:y.event)==="*"||((k=(w=m.filter)===null||w===void 0?void 0:w.event)===null||k===void 0?void 0:k.toLocaleLowerCase())===o}).map(m=>m.callback(f,r)):(i=this.bindings[o])===null||i===void 0||i.filter(m=>{var y,w,k,g,p,v;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in m){const _=m.id,S=(y=m.filter)===null||y===void 0?void 0:y.event;return _&&((w=n.ids)===null||w===void 0?void 0:w.includes(_))&&(S==="*"||(S==null?void 0:S.toLocaleLowerCase())===((k=n.data)===null||k===void 0?void 0:k.type.toLocaleLowerCase()))}else{const _=(p=(g=m==null?void 0:m.filter)===null||g===void 0?void 0:g.event)===null||p===void 0?void 0:p.toLocaleLowerCase();return _==="*"||_===((v=n==null?void 0:n.event)===null||v===void 0?void 0:v.toLocaleLowerCase())}else return m.type.toLocaleLowerCase()===o}).map(m=>{if(typeof f=="object"&&"ids"in f){const y=f.data,{schema:w,table:k,commit_timestamp:g,type:p,errors:v}=y;f=Object.assign(Object.assign({},{schema:w,table:k,commit_timestamp:g,eventType:p,new:{},old:{},errors:v}),this._getPayloadRecords(y))}m.callback(f,r)})}_isClosed(){return this.state===pe.closed}_isJoined(){return this.state===pe.joined}_isJoining(){return this.state===pe.joining}_isLeaving(){return this.state===pe.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,n,r){const s=t.toLocaleLowerCase(),i={type:s,filter:n,callback:r};return this.bindings[s]?this.bindings[s].push(i):this.bindings[s]=[i],this}_off(t,n){const r=t.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(s=>{var i;return!(((i=s.type)===null||i===void 0?void 0:i.toLocaleLowerCase())===r&&Aa.isEqual(s.filter,n))}),this}static isEqual(t,n){if(Object.keys(t).length!==Object.keys(n).length)return!1;for(const r in t)if(t[r]!==n[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(He.close,{},t)}_onError(t){this._on(He.error,{},n=>t(n))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=pe.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const n={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(n.new=rc(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(n.old=rc(t.columns,t.old_record)),n}}const lc=()=>{},Dv=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class Mv{constructor(t,n){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers=Nv,this.params={},this.timeout=df,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=lc,this.ref=0,this.logger=lc,this.conn=null,this.sendBuffer=[],this.serializer=new Tv,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=i=>{let o;return i?o=i:typeof fetch>"u"?o=(...l)=>Xr(async()=>{const{default:a}=await Promise.resolve().then(()=>Jn);return{default:a}},void 0).then(({default:a})=>a(...l)):o=fetch,(...l)=>o(...l)},this.endPoint=`${t}/${jl.websocket}`,this.httpEndpoint=pf(t),n!=null&&n.transport?this.transport=n.transport:this.transport=null,n!=null&&n.params&&(this.params=n.params),n!=null&&n.headers&&(this.headers=Object.assign(Object.assign({},this.headers),n.headers)),n!=null&&n.timeout&&(this.timeout=n.timeout),n!=null&&n.logger&&(this.logger=n.logger),(n!=null&&n.logLevel||n!=null&&n.log_level)&&(this.logLevel=n.logLevel||n.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),n!=null&&n.heartbeatIntervalMs&&(this.heartbeatIntervalMs=n.heartbeatIntervalMs);const s=(r=n==null?void 0:n.params)===null||r===void 0?void 0:r.apikey;if(s&&(this.accessTokenValue=s,this.apiKey=s),this.reconnectAfterMs=n!=null&&n.reconnectAfterMs?n.reconnectAfterMs:i=>[1e3,2e3,5e3,1e4][i-1]||1e4,this.encode=n!=null&&n.encode?n.encode:(i,o)=>o(JSON.stringify(i)),this.decode=n!=null&&n.decode?n.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new hf(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(n==null?void 0:n.fetch),n!=null&&n.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(n==null?void 0:n.worker)||!1,this.workerUrl=n==null?void 0:n.workerUrl}this.accessToken=(n==null?void 0:n.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=Ev),this.transport){typeof window<"u"&&this.transport===window.WebSocket?this.conn=new this.transport(this.endpointURL()):this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection();return}this.conn=new Uv(this.endpointURL(),void 0,{close:()=>{this.conn=null}})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:Cv}))}disconnect(t,n){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,n??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(r=>r.teardown()))}getChannels(){return this.channels}async removeChannel(t){const n=await t.unsubscribe();return this.channels=this.channels.filter(r=>r._joinRef!==t._joinRef),this.channels.length===0&&this.disconnect(),n}async removeAllChannels(){const t=await Promise.all(this.channels.map(n=>n.unsubscribe()));return this.channels=[],this.disconnect(),t}log(t,n,r){this.logger(t,n,r)}connectionState(){switch(this.conn&&this.conn.readyState){case $n.connecting:return Gt.Connecting;case $n.open:return Gt.Open;case $n.closing:return Gt.Closing;default:return Gt.Closed}}isConnected(){return this.connectionState()===Gt.Open}channel(t,n={config:{}}){const r=`realtime:${t}`,s=this.getChannels().find(i=>i.topic===r);if(s)return s;{const i=new Aa(`realtime:${t}`,n,this);return this.channels.push(i),i}}push(t){const{topic:n,event:r,payload:s,ref:i}=t,o=()=>{this.encode(t,l=>{var a;(a=this.conn)===null||a===void 0||a.send(l)})};this.log("push",`${n} ${r} (${i})`,s),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(t=null){let n=t||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=n&&(this.accessTokenValue=n,this.channels.forEach(r=>{n&&r.updateJoinPayload({access_token:n,version:this.headers&&this.headers["X-Client-Info"]}),r.joinedOnce&&r._isJoined()&&r._push(He.access_token,{access_token:n})}))}async sendHeartbeat(){var t;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(t=this.conn)===null||t===void 0||t.close(Pv,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(t){this.heartbeatCallback=t}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let n=this.channels.find(r=>r.topic===t&&(r._isJoined()||r._isJoining()));n&&(this.log("transport",`leaving duplicate topic "${t}"`),n.unsubscribe())}_remove(t){this.channels=this.channels.filter(n=>n.topic!==t.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,n=>{let{topic:r,event:s,payload:i,ref:o}=n;r==="phoenix"&&s==="phx_reply"&&this.heartbeatCallback(n.payload.status=="ok"?"ok":"error"),o&&o===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${r} ${s} ${o&&"("+o+")"||""}`,i),Array.from(this.channels).filter(l=>l._isMember(r)).forEach(l=>l._trigger(s,i,o)),this.stateChangeCallbacks.message.forEach(l=>l(n))})}_onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),!this.worker)this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);else{this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=n=>{this.log("worker","worker error",n.message),this.workerRef.terminate()},this.workerRef.onmessage=n=>{n.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}this.stateChangeCallbacks.open.forEach(t=>t())}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(n=>n(t))}_onConnError(t){this.log("transport",t.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(n=>n(t))}_triggerChanError(){this.channels.forEach(t=>t._trigger(He.error))}_appendParams(t,n){if(Object.keys(n).length===0)return t;const r=t.match(/\?/)?"&":"?",s=new URLSearchParams(n);return`${t}${r}${s}`}_workerObjectUrl(t){let n;if(t)n=t;else{const r=new Blob([Dv],{type:"application/javascript"});n=URL.createObjectURL(r)}return n}}class Uv{constructor(t,n,r){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=$n.connecting,this.send=()=>{},this.url=null,this.url=t,this.close=r.close}}class Da extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function re(e){return typeof e=="object"&&e!==null&&"__isStorageError"in e}class zv extends Da{constructor(t,n){super(t),this.name="StorageApiError",this.status=n}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class bl extends Da{constructor(t,n){super(t),this.name="StorageUnknownError",this.originalError=n}}var Fv=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{u(r.next(d))}catch(h){o(h)}}function a(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};const mf=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>Xr(async()=>{const{default:r}=await Promise.resolve().then(()=>Jn);return{default:r}},void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},Bv=()=>Fv(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield Xr(()=>Promise.resolve().then(()=>Jn),void 0)).Response:Response}),Nl=e=>{if(Array.isArray(e))return e.map(n=>Nl(n));if(typeof e=="function"||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([n,r])=>{const s=n.replace(/([-_][a-z])/gi,i=>i.toUpperCase().replace(/[-_]/g,""));t[s]=Nl(r)}),t};var un=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{u(r.next(d))}catch(h){o(h)}}function a(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};const xo=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Hv=(e,t,n)=>un(void 0,void 0,void 0,function*(){const r=yield Bv();e instanceof r&&!(n!=null&&n.noResolveJson)?e.json().then(s=>{t(new zv(xo(s),e.status||500))}).catch(s=>{t(new bl(xo(s),s))}):t(new bl(xo(e),e))}),Wv=(e,t,n,r)=>{const s={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?s:(s.headers=Object.assign({"Content-Type":"application/json"},t==null?void 0:t.headers),r&&(s.body=JSON.stringify(r)),Object.assign(Object.assign({},s),n))};function es(e,t,n,r,s,i){return un(this,void 0,void 0,function*(){return new Promise((o,l)=>{e(n,Wv(t,r,s,i)).then(a=>{if(!a.ok)throw a;return r!=null&&r.noResolveJson?a:a.json()}).then(a=>o(a)).catch(a=>Hv(a,l,r))})})}function fi(e,t,n,r){return un(this,void 0,void 0,function*(){return es(e,"GET",t,n,r)})}function _t(e,t,n,r,s){return un(this,void 0,void 0,function*(){return es(e,"POST",t,r,s,n)})}function qv(e,t,n,r,s){return un(this,void 0,void 0,function*(){return es(e,"PUT",t,r,s,n)})}function Vv(e,t,n,r){return un(this,void 0,void 0,function*(){return es(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)})}function gf(e,t,n,r,s){return un(this,void 0,void 0,function*(){return es(e,"DELETE",t,r,s,n)})}var we=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{u(r.next(d))}catch(h){o(h)}}function a(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};const Kv={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},ac={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Qv{constructor(t,n={},r,s){this.url=t,this.headers=n,this.bucketId=r,this.fetch=mf(s)}uploadOrUpdate(t,n,r,s){return we(this,void 0,void 0,function*(){try{let i;const o=Object.assign(Object.assign({},ac),s);let l=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(o.upsert)});const a=o.metadata;typeof Blob<"u"&&r instanceof Blob?(i=new FormData,i.append("cacheControl",o.cacheControl),a&&i.append("metadata",this.encodeMetadata(a)),i.append("",r)):typeof FormData<"u"&&r instanceof FormData?(i=r,i.append("cacheControl",o.cacheControl),a&&i.append("metadata",this.encodeMetadata(a))):(i=r,l["cache-control"]=`max-age=${o.cacheControl}`,l["content-type"]=o.contentType,a&&(l["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),s!=null&&s.headers&&(l=Object.assign(Object.assign({},l),s.headers));const u=this._removeEmptyFolders(n),d=this._getFinalPath(u),h=yield this.fetch(`${this.url}/object/${d}`,Object.assign({method:t,body:i,headers:l},o!=null&&o.duplex?{duplex:o.duplex}:{})),f=yield h.json();return h.ok?{data:{path:u,id:f.Id,fullPath:f.Key},error:null}:{data:null,error:f}}catch(i){if(re(i))return{data:null,error:i};throw i}})}upload(t,n,r){return we(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,n,r)})}uploadToSignedUrl(t,n,r,s){return we(this,void 0,void 0,function*(){const i=this._removeEmptyFolders(t),o=this._getFinalPath(i),l=new URL(this.url+`/object/upload/sign/${o}`);l.searchParams.set("token",n);try{let a;const u=Object.assign({upsert:ac.upsert},s),d=Object.assign(Object.assign({},this.headers),{"x-upsert":String(u.upsert)});typeof Blob<"u"&&r instanceof Blob?(a=new FormData,a.append("cacheControl",u.cacheControl),a.append("",r)):typeof FormData<"u"&&r instanceof FormData?(a=r,a.append("cacheControl",u.cacheControl)):(a=r,d["cache-control"]=`max-age=${u.cacheControl}`,d["content-type"]=u.contentType);const h=yield this.fetch(l.toString(),{method:"PUT",body:a,headers:d}),f=yield h.json();return h.ok?{data:{path:i,fullPath:f.Key},error:null}:{data:null,error:f}}catch(a){if(re(a))return{data:null,error:a};throw a}})}createSignedUploadUrl(t,n){return we(this,void 0,void 0,function*(){try{let r=this._getFinalPath(t);const s=Object.assign({},this.headers);n!=null&&n.upsert&&(s["x-upsert"]="true");const i=yield _t(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:s}),o=new URL(this.url+i.url),l=o.searchParams.get("token");if(!l)throw new Da("No token returned by API");return{data:{signedUrl:o.toString(),path:t,token:l},error:null}}catch(r){if(re(r))return{data:null,error:r};throw r}})}update(t,n,r){return we(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,n,r)})}move(t,n,r){return we(this,void 0,void 0,function*(){try{return{data:yield _t(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(s){if(re(s))return{data:null,error:s};throw s}})}copy(t,n,r){return we(this,void 0,void 0,function*(){try{return{data:{path:(yield _t(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(s){if(re(s))return{data:null,error:s};throw s}})}createSignedUrl(t,n,r){return we(this,void 0,void 0,function*(){try{let s=this._getFinalPath(t),i=yield _t(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:n},r!=null&&r.transform?{transform:r.transform}:{}),{headers:this.headers});const o=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${o}`)},{data:i,error:null}}catch(s){if(re(s))return{data:null,error:s};throw s}})}createSignedUrls(t,n,r){return we(this,void 0,void 0,function*(){try{const s=yield _t(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:n,paths:t},{headers:this.headers}),i=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return{data:s.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${i}`):null})),error:null}}catch(s){if(re(s))return{data:null,error:s};throw s}})}download(t,n){return we(this,void 0,void 0,function*(){const s=typeof(n==null?void 0:n.transform)<"u"?"render/image/authenticated":"object",i=this.transformOptsToQueryString((n==null?void 0:n.transform)||{}),o=i?`?${i}`:"";try{const l=this._getFinalPath(t);return{data:yield(yield fi(this.fetch,`${this.url}/${s}/${l}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(l){if(re(l))return{data:null,error:l};throw l}})}info(t){return we(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{const r=yield fi(this.fetch,`${this.url}/object/info/${n}`,{headers:this.headers});return{data:Nl(r),error:null}}catch(r){if(re(r))return{data:null,error:r};throw r}})}exists(t){return we(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{return yield Vv(this.fetch,`${this.url}/object/${n}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(re(r)&&r instanceof bl){const s=r.originalError;if([400,404].includes(s==null?void 0:s.status))return{data:!1,error:r}}throw r}})}getPublicUrl(t,n){const r=this._getFinalPath(t),s=[],i=n!=null&&n.download?`download=${n.download===!0?"":n.download}`:"";i!==""&&s.push(i);const l=typeof(n==null?void 0:n.transform)<"u"?"render/image":"object",a=this.transformOptsToQueryString((n==null?void 0:n.transform)||{});a!==""&&s.push(a);let u=s.join("&");return u!==""&&(u=`?${u}`),{data:{publicUrl:encodeURI(`${this.url}/${l}/public/${r}${u}`)}}}remove(t){return we(this,void 0,void 0,function*(){try{return{data:yield gf(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(n){if(re(n))return{data:null,error:n};throw n}})}list(t,n,r){return we(this,void 0,void 0,function*(){try{const s=Object.assign(Object.assign(Object.assign({},Kv),n),{prefix:t||""});return{data:yield _t(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},r),error:null}}catch(s){if(re(s))return{data:null,error:s};throw s}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const n=[];return t.width&&n.push(`width=${t.width}`),t.height&&n.push(`height=${t.height}`),t.resize&&n.push(`resize=${t.resize}`),t.format&&n.push(`format=${t.format}`),t.quality&&n.push(`quality=${t.quality}`),n.join("&")}}const Gv="2.7.1",Jv={"X-Client-Info":`storage-js/${Gv}`};var dn=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{u(r.next(d))}catch(h){o(h)}}function a(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};class Yv{constructor(t,n={},r){this.url=t,this.headers=Object.assign(Object.assign({},Jv),n),this.fetch=mf(r)}listBuckets(){return dn(this,void 0,void 0,function*(){try{return{data:yield fi(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(re(t))return{data:null,error:t};throw t}})}getBucket(t){return dn(this,void 0,void 0,function*(){try{return{data:yield fi(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(n){if(re(n))return{data:null,error:n};throw n}})}createBucket(t,n={public:!1}){return dn(this,void 0,void 0,function*(){try{return{data:yield _t(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(re(r))return{data:null,error:r};throw r}})}updateBucket(t,n){return dn(this,void 0,void 0,function*(){try{return{data:yield qv(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(re(r))return{data:null,error:r};throw r}})}emptyBucket(t){return dn(this,void 0,void 0,function*(){try{return{data:yield _t(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(n){if(re(n))return{data:null,error:n};throw n}})}deleteBucket(t){return dn(this,void 0,void 0,function*(){try{return{data:yield gf(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(n){if(re(n))return{data:null,error:n};throw n}})}}class Xv extends Yv{constructor(t,n={},r){super(t,n,r)}from(t){return new Qv(this.url,this.headers,t,this.fetch)}}const Zv="2.50.0";let fr="";typeof Deno<"u"?fr="deno":typeof document<"u"?fr="web":typeof navigator<"u"&&navigator.product==="ReactNative"?fr="react-native":fr="node";const ey={"X-Client-Info":`supabase-js-${fr}/${Zv}`},ty={headers:ey},ny={schema:"public"},ry={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},sy={};var iy=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{u(r.next(d))}catch(h){o(h)}}function a(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};const oy=e=>{let t;return e?t=e:typeof fetch>"u"?t=Zh:t=fetch,(...n)=>t(...n)},ly=()=>typeof Headers>"u"?ef:Headers,ay=(e,t,n)=>{const r=oy(n),s=ly();return(i,o)=>iy(void 0,void 0,void 0,function*(){var l;const a=(l=yield t())!==null&&l!==void 0?l:e;let u=new s(o==null?void 0:o.headers);return u.has("apikey")||u.set("apikey",e),u.has("Authorization")||u.set("Authorization",`Bearer ${a}`),r(i,Object.assign(Object.assign({},o),{headers:u}))})};var uy=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{u(r.next(d))}catch(h){o(h)}}function a(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};function cy(e){return e.endsWith("/")?e:e+"/"}function dy(e,t){var n,r;const{db:s,auth:i,realtime:o,global:l}=e,{db:a,auth:u,realtime:d,global:h}=t,f={db:Object.assign(Object.assign({},a),s),auth:Object.assign(Object.assign({},u),i),realtime:Object.assign(Object.assign({},d),o),global:Object.assign(Object.assign(Object.assign({},h),l),{headers:Object.assign(Object.assign({},(n=h==null?void 0:h.headers)!==null&&n!==void 0?n:{}),(r=l==null?void 0:l.headers)!==null&&r!==void 0?r:{})}),accessToken:()=>uy(this,void 0,void 0,function*(){return""})};return e.accessToken?f.accessToken=e.accessToken:delete f.accessToken,f}const vf="2.70.0",mn=30*1e3,Cl=3,_o=Cl*mn,hy="http://localhost:9999",fy="supabase.auth.token",py={"X-Client-Info":`gotrue-js/${vf}`},Pl="X-Supabase-Api-Version",yf={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},my=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,gy=6e5;class Ma extends Error{constructor(t,n,r){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=n,this.code=r}}function O(e){return typeof e=="object"&&e!==null&&"__isAuthError"in e}class vy extends Ma{constructor(t,n,r){super(t,n,r),this.name="AuthApiError",this.status=n,this.code=r}}function yy(e){return O(e)&&e.name==="AuthApiError"}class wf extends Ma{constructor(t,n){super(t),this.name="AuthUnknownError",this.originalError=n}}class Bt extends Ma{constructor(t,n,r,s){super(t,r,s),this.name=n,this.status=r}}class yt extends Bt{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function wy(e){return O(e)&&e.name==="AuthSessionMissingError"}class _s extends Bt{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class ks extends Bt{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class Ss extends Bt{constructor(t,n=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function xy(e){return O(e)&&e.name==="AuthImplicitGrantRedirectError"}class uc extends Bt{constructor(t,n=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Tl extends Bt{constructor(t,n){super(t,"AuthRetryableFetchError",n,void 0)}}function ko(e){return O(e)&&e.name==="AuthRetryableFetchError"}class cc extends Bt{constructor(t,n,r){super(t,"AuthWeakPasswordError",n,"weak_password"),this.reasons=r}}class jr extends Bt{constructor(t){super(t,"AuthInvalidJwtError",400,"invalid_jwt")}}const pi="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),dc=` 	
\r=`.split(""),_y=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<dc.length;t+=1)e[dc[t].charCodeAt(0)]=-2;for(let t=0;t<pi.length;t+=1)e[pi[t].charCodeAt(0)]=t;return e})();function hc(e,t,n){if(e!==null)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(pi[r]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(pi[r]),t.queuedBits-=6}}function xf(e,t,n){const r=_y[e];if(r>-1)for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)n(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else{if(r===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}}function fc(e){const t=[],n=o=>{t.push(String.fromCodePoint(o))},r={utf8seq:0,codepoint:0},s={queue:0,queuedBits:0},i=o=>{jy(o,r,n)};for(let o=0;o<e.length;o+=1)xf(e.charCodeAt(o),s,i);return t.join("")}function ky(e,t){if(e<=127){t(e);return}else if(e<=2047){t(192|e>>6),t(128|e&63);return}else if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|e&63);return}else if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|e&63);return}throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function Sy(e,t){for(let n=0;n<e.length;n+=1){let r=e.charCodeAt(n);if(r>55295&&r<=56319){const s=(r-55296)*1024&65535;r=(e.charCodeAt(n+1)-56320&65535|s)+65536,n+=1}ky(r,t)}}function jy(e,t,n){if(t.utf8seq===0){if(e<=127){n(e);return}for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(t.utf8seq===2)t.codepoint=e&31;else if(t.utf8seq===3)t.codepoint=e&15;else if(t.utf8seq===4)t.codepoint=e&7;else throw new Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|e&63,t.utf8seq-=1,t.utf8seq===0&&n(t.codepoint)}}function Ey(e){const t=[],n={queue:0,queuedBits:0},r=s=>{t.push(s)};for(let s=0;s<e.length;s+=1)xf(e.charCodeAt(s),n,r);return new Uint8Array(t)}function by(e){const t=[];return Sy(e,n=>t.push(n)),new Uint8Array(t)}function Ny(e){const t=[],n={queue:0,queuedBits:0},r=s=>{t.push(s)};return e.forEach(s=>hc(s,n,r)),hc(null,n,r),t.join("")}function Cy(e){return Math.round(Date.now()/1e3)+e}function Py(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e=="x"?t:t&3|8).toString(16)})}const ze=()=>typeof window<"u"&&typeof document<"u",qt={tested:!1,writable:!1},Er=()=>{if(!ze())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(qt.tested)return qt.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),qt.tested=!0,qt.writable=!0}catch{qt.tested=!0,qt.writable=!1}return qt.writable};function Ty(e){const t={},n=new URL(e);if(n.hash&&n.hash[0]==="#")try{new URLSearchParams(n.hash.substring(1)).forEach((s,i)=>{t[i]=s})}catch{}return n.searchParams.forEach((r,s)=>{t[s]=r}),t}const _f=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>Xr(async()=>{const{default:r}=await Promise.resolve().then(()=>Jn);return{default:r}},void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},Ry=e=>typeof e=="object"&&e!==null&&"status"in e&&"ok"in e&&"json"in e&&typeof e.json=="function",kf=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},js=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch{return n}},Es=async(e,t)=>{await e.removeItem(t)};class zi{constructor(){this.promise=new zi.promiseConstructor((t,n)=>{this.resolve=t,this.reject=n})}}zi.promiseConstructor=Promise;function So(e){const t=e.split(".");if(t.length!==3)throw new jr("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!my.test(t[r]))throw new jr("JWT not in base64url format");return{header:JSON.parse(fc(t[0])),payload:JSON.parse(fc(t[1])),signature:Ey(t[2]),raw:{header:t[0],payload:t[1]}}}async function Oy(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function Ly(e,t){return new Promise((r,s)=>{(async()=>{for(let i=0;i<1/0;i++)try{const o=await e(i);if(!t(i,null,o)){r(o);return}}catch(o){if(!t(i,o)){s(o);return}}})()})}function $y(e){return("0"+e.toString(16)).substr(-2)}function Iy(){const t=new Uint32Array(56);if(typeof crypto>"u"){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=n.length;let s="";for(let i=0;i<56;i++)s+=n.charAt(Math.floor(Math.random()*r));return s}return crypto.getRandomValues(t),Array.from(t,$y).join("")}async function Ay(e){const n=new TextEncoder().encode(e),r=await crypto.subtle.digest("SHA-256",n),s=new Uint8Array(r);return Array.from(s).map(i=>String.fromCharCode(i)).join("")}async function Dy(e){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const n=await Ay(e);return btoa(n).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function hn(e,t,n=!1){const r=Iy();let s=r;n&&(s+="/PASSWORD_RECOVERY"),await kf(e,`${t}-code-verifier`,s);const i=await Dy(r);return[i,r===i?"plain":"s256"]}const My=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function Uy(e){const t=e.headers.get(Pl);if(!t||!t.match(My))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch{return null}}function zy(e){if(!e)throw new Error("Missing exp claim");const t=Math.floor(Date.now()/1e3);if(e<=t)throw new Error("JWT has expired")}function Fy(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const By=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function fn(e){if(!By.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var Hy=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n};const Qt=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Wy=[502,503,504];async function pc(e){var t;if(!Ry(e))throw new Tl(Qt(e),0);if(Wy.includes(e.status))throw new Tl(Qt(e),e.status);let n;try{n=await e.json()}catch(i){throw new wf(Qt(i),i)}let r;const s=Uy(e);if(s&&s.getTime()>=yf["2024-01-01"].timestamp&&typeof n=="object"&&n&&typeof n.code=="string"?r=n.code:typeof n=="object"&&n&&typeof n.error_code=="string"&&(r=n.error_code),r){if(r==="weak_password")throw new cc(Qt(n),e.status,((t=n.weak_password)===null||t===void 0?void 0:t.reasons)||[]);if(r==="session_not_found")throw new yt}else if(typeof n=="object"&&n&&typeof n.weak_password=="object"&&n.weak_password&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.reasons.reduce((i,o)=>i&&typeof o=="string",!0))throw new cc(Qt(n),e.status,n.weak_password.reasons);throw new vy(Qt(n),e.status||500,r)}const qy=(e,t,n,r)=>{const s={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?s:(s.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t==null?void 0:t.headers),s.body=JSON.stringify(r),Object.assign(Object.assign({},s),n))};async function I(e,t,n,r){var s;const i=Object.assign({},r==null?void 0:r.headers);i[Pl]||(i[Pl]=yf["2024-01-01"].name),r!=null&&r.jwt&&(i.Authorization=`Bearer ${r.jwt}`);const o=(s=r==null?void 0:r.query)!==null&&s!==void 0?s:{};r!=null&&r.redirectTo&&(o.redirect_to=r.redirectTo);const l=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",a=await Vy(e,t,n+l,{headers:i,noResolveJson:r==null?void 0:r.noResolveJson},{},r==null?void 0:r.body);return r!=null&&r.xform?r==null?void 0:r.xform(a):{data:Object.assign({},a),error:null}}async function Vy(e,t,n,r,s,i){const o=qy(t,r,s,i);let l;try{l=await e(n,Object.assign({},o))}catch(a){throw console.error(a),new Tl(Qt(a),0)}if(l.ok||await pc(l),r!=null&&r.noResolveJson)return l;try{return await l.json()}catch(a){await pc(a)}}function nt(e){var t;let n=null;Jy(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=Cy(e.expires_in)));const r=(t=e.user)!==null&&t!==void 0?t:e;return{data:{session:n,user:r},error:null}}function mc(e){const t=nt(e);return!t.error&&e.weak_password&&typeof e.weak_password=="object"&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&typeof e.weak_password.message=="string"&&e.weak_password.reasons.reduce((n,r)=>n&&typeof r=="string",!0)&&(t.data.weak_password=e.weak_password),t}function jt(e){var t;return{data:{user:(t=e.user)!==null&&t!==void 0?t:e},error:null}}function Ky(e){return{data:e,error:null}}function Qy(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:s,verification_type:i}=e,o=Hy(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),l={action_link:t,email_otp:n,hashed_token:r,redirect_to:s,verification_type:i},a=Object.assign({},o);return{data:{properties:l,user:a},error:null}}function Gy(e){return e}function Jy(e){return e.access_token&&e.refresh_token&&e.expires_in}const jo=["global","local","others"];var Yy=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n};class Xy{constructor({url:t="",headers:n={},fetch:r}){this.url=t,this.headers=n,this.fetch=_f(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,n=jo[0]){if(jo.indexOf(n)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${jo.join(", ")}`);try{return await I(this.fetch,"POST",`${this.url}/logout?scope=${n}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(r){if(O(r))return{data:null,error:r};throw r}}async inviteUserByEmail(t,n={}){try{return await I(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:n.data},headers:this.headers,redirectTo:n.redirectTo,xform:jt})}catch(r){if(O(r))return{data:{user:null},error:r};throw r}}async generateLink(t){try{const{options:n}=t,r=Yy(t,["options"]),s=Object.assign(Object.assign({},r),n);return"newEmail"in r&&(s.new_email=r==null?void 0:r.newEmail,delete s.newEmail),await I(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:Qy,redirectTo:n==null?void 0:n.redirectTo})}catch(n){if(O(n))return{data:{properties:null,user:null},error:n};throw n}}async createUser(t){try{return await I(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:jt})}catch(n){if(O(n))return{data:{user:null},error:n};throw n}}async listUsers(t){var n,r,s,i,o,l,a;try{const u={nextPage:null,lastPage:0,total:0},d=await I(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(n=t==null?void 0:t.page)===null||n===void 0?void 0:n.toString())!==null&&r!==void 0?r:"",per_page:(i=(s=t==null?void 0:t.perPage)===null||s===void 0?void 0:s.toString())!==null&&i!==void 0?i:""},xform:Gy});if(d.error)throw d.error;const h=await d.json(),f=(o=d.headers.get("x-total-count"))!==null&&o!==void 0?o:0,m=(a=(l=d.headers.get("link"))===null||l===void 0?void 0:l.split(","))!==null&&a!==void 0?a:[];return m.length>0&&(m.forEach(y=>{const w=parseInt(y.split(";")[0].split("=")[1].substring(0,1)),k=JSON.parse(y.split(";")[1].split("=")[1]);u[`${k}Page`]=w}),u.total=parseInt(f)),{data:Object.assign(Object.assign({},h),u),error:null}}catch(u){if(O(u))return{data:{users:[]},error:u};throw u}}async getUserById(t){fn(t);try{return await I(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:jt})}catch(n){if(O(n))return{data:{user:null},error:n};throw n}}async updateUserById(t,n){fn(t);try{return await I(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:n,headers:this.headers,xform:jt})}catch(r){if(O(r))return{data:{user:null},error:r};throw r}}async deleteUser(t,n=!1){fn(t);try{return await I(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:n},xform:jt})}catch(r){if(O(r))return{data:{user:null},error:r};throw r}}async _listFactors(t){fn(t.userId);try{const{data:n,error:r}=await I(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:s=>({data:{factors:s},error:null})});return{data:n,error:r}}catch(n){if(O(n))return{data:null,error:n};throw n}}async _deleteFactor(t){fn(t.userId),fn(t.id);try{return{data:await I(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(n){if(O(n))return{data:null,error:n};throw n}}}const Zy={getItem:e=>Er()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{Er()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{Er()&&globalThis.localStorage.removeItem(e)}};function gc(e={}){return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}function ew(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const pn={debug:!!(globalThis&&Er()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class Sf extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class tw extends Sf{}async function nw(e,t,n){pn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),pn.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,t===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async s=>{if(s){pn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,s.name);try{return await n()}finally{pn.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,s.name)}}else{if(t===0)throw pn.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new tw(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(pn.debug)try{const i=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(i,null,"  "))}catch(i){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",i)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}}))}ew();const rw={url:hy,storageKey:fy,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:py,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function vc(e,t,n){return await n()}class Wr{constructor(t){var n,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=Wr.nextInstanceID,Wr.nextInstanceID+=1,this.instanceID>0&&ze()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const s=Object.assign(Object.assign({},rw),t);if(this.logDebugMessages=!!s.debug,typeof s.debug=="function"&&(this.logger=s.debug),this.persistSession=s.persistSession,this.storageKey=s.storageKey,this.autoRefreshToken=s.autoRefreshToken,this.admin=new Xy({url:s.url,headers:s.headers,fetch:s.fetch}),this.url=s.url,this.headers=s.headers,this.fetch=_f(s.fetch),this.lock=s.lock||vc,this.detectSessionInUrl=s.detectSessionInUrl,this.flowType=s.flowType,this.hasCustomAuthorizationHeader=s.hasCustomAuthorizationHeader,s.lock?this.lock=s.lock:ze()&&(!((n=globalThis==null?void 0:globalThis.navigator)===null||n===void 0)&&n.locks)?this.lock=nw:this.lock=vc,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?s.storage?this.storage=s.storage:Er()?this.storage=Zy:(this.memoryStorage={},this.storage=gc(this.memoryStorage)):(this.memoryStorage={},this.storage=gc(this.memoryStorage)),ze()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",i)}(r=this.broadcastChannel)===null||r===void 0||r.addEventListener("message",async i=>{this._debug("received broadcast notification from other tab or client",i),await this._notifyAllSubscribers(i.data.event,i.data.session,!1)})}this.initialize()}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${vf}) ${new Date().toISOString()}`,...t),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var t;try{const n=Ty(window.location.href);let r="none";if(this._isImplicitGrantCallback(n)?r="implicit":await this._isPKCECallback(n)&&(r="pkce"),ze()&&this.detectSessionInUrl&&r!=="none"){const{data:s,error:i}=await this._getSessionFromURL(n,r);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),xy(i)){const a=(t=i.details)===null||t===void 0?void 0:t.code;if(a==="identity_already_exists"||a==="identity_not_found"||a==="single_identity_not_deletable")return{error:i}}return await this._removeSession(),{error:i}}const{session:o,redirectType:l}=s;return this._debug("#_initialize()","detected session in URL",o,"redirect type",l),await this._saveSession(o),setTimeout(async()=>{l==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(n){return O(n)?{error:n}:{error:new wf("Unexpected error during initialization",n)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var n,r,s;try{const i=await I(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(r=(n=t==null?void 0:t.options)===null||n===void 0?void 0:n.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:(s=t==null?void 0:t.options)===null||s===void 0?void 0:s.captchaToken}},xform:nt}),{data:o,error:l}=i;if(l||!o)return{data:{user:null,session:null},error:l};const a=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:u,session:a},error:null}}catch(i){if(O(i))return{data:{user:null,session:null},error:i};throw i}}async signUp(t){var n,r,s;try{let i;if("email"in t){const{email:d,password:h,options:f}=t;let m=null,y=null;this.flowType==="pkce"&&([m,y]=await hn(this.storage,this.storageKey)),i=await I(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:f==null?void 0:f.emailRedirectTo,body:{email:d,password:h,data:(n=f==null?void 0:f.data)!==null&&n!==void 0?n:{},gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken},code_challenge:m,code_challenge_method:y},xform:nt})}else if("phone"in t){const{phone:d,password:h,options:f}=t;i=await I(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:d,password:h,data:(r=f==null?void 0:f.data)!==null&&r!==void 0?r:{},channel:(s=f==null?void 0:f.channel)!==null&&s!==void 0?s:"sms",gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken}},xform:nt})}else throw new ks("You must provide either an email or phone number and a password");const{data:o,error:l}=i;if(l||!o)return{data:{user:null,session:null},error:l};const a=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:u,session:a},error:null}}catch(i){if(O(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithPassword(t){try{let n;if("email"in t){const{email:i,password:o,options:l}=t;n=await I(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:i,password:o,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken}},xform:mc})}else if("phone"in t){const{phone:i,password:o,options:l}=t;n=await I(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:i,password:o,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken}},xform:mc})}else throw new ks("You must provide either an email or phone number and a password");const{data:r,error:s}=n;return s?{data:{user:null,session:null},error:s}:!r||!r.session||!r.user?{data:{user:null,session:null},error:new _s}:(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:s})}catch(n){if(O(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOAuth(t){var n,r,s,i;return await this._handleProviderSignIn(t.provider,{redirectTo:(n=t.options)===null||n===void 0?void 0:n.redirectTo,scopes:(r=t.options)===null||r===void 0?void 0:r.scopes,queryParams:(s=t.options)===null||s===void 0?void 0:s.queryParams,skipBrowserRedirect:(i=t.options)===null||i===void 0?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(t))}async signInWithWeb3(t){const{chain:n}=t;if(n==="solana")return await this.signInWithSolana(t);throw new Error(`@supabase/auth-js: Unsupported chain "${n}"`)}async signInWithSolana(t){var n,r,s,i,o,l,a,u,d,h,f,m;let y,w;if("message"in t)y=t.message,w=t.signature;else{const{chain:k,wallet:g,statement:p,options:v}=t;let _;if(ze())if(typeof g=="object")_=g;else{const E=window;if("solana"in E&&typeof E.solana=="object"&&("signIn"in E.solana&&typeof E.solana.signIn=="function"||"signMessage"in E.solana&&typeof E.solana.signMessage=="function"))_=E.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof g!="object"||!(v!=null&&v.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");_=g}const S=new URL((n=v==null?void 0:v.url)!==null&&n!==void 0?n:window.location.href);if("signIn"in _&&_.signIn){const E=await _.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},v==null?void 0:v.signInWithSolana),{version:"1",domain:S.host,uri:S.href}),p?{statement:p}:null));let b;if(Array.isArray(E)&&E[0]&&typeof E[0]=="object")b=E[0];else if(E&&typeof E=="object"&&"signedMessage"in E&&"signature"in E)b=E;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in b&&"signature"in b&&(typeof b.signedMessage=="string"||b.signedMessage instanceof Uint8Array)&&b.signature instanceof Uint8Array)y=typeof b.signedMessage=="string"?b.signedMessage:new TextDecoder().decode(b.signedMessage),w=b.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in _)||typeof _.signMessage!="function"||!("publicKey"in _)||typeof _!="object"||!_.publicKey||!("toBase58"in _.publicKey)||typeof _.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");y=[`${S.host} wants you to sign in with your Solana account:`,_.publicKey.toBase58(),...p?["",p,""]:[""],"Version: 1",`URI: ${S.href}`,`Issued At: ${(s=(r=v==null?void 0:v.signInWithSolana)===null||r===void 0?void 0:r.issuedAt)!==null&&s!==void 0?s:new Date().toISOString()}`,...!((i=v==null?void 0:v.signInWithSolana)===null||i===void 0)&&i.notBefore?[`Not Before: ${v.signInWithSolana.notBefore}`]:[],...!((o=v==null?void 0:v.signInWithSolana)===null||o===void 0)&&o.expirationTime?[`Expiration Time: ${v.signInWithSolana.expirationTime}`]:[],...!((l=v==null?void 0:v.signInWithSolana)===null||l===void 0)&&l.chainId?[`Chain ID: ${v.signInWithSolana.chainId}`]:[],...!((a=v==null?void 0:v.signInWithSolana)===null||a===void 0)&&a.nonce?[`Nonce: ${v.signInWithSolana.nonce}`]:[],...!((u=v==null?void 0:v.signInWithSolana)===null||u===void 0)&&u.requestId?[`Request ID: ${v.signInWithSolana.requestId}`]:[],...!((h=(d=v==null?void 0:v.signInWithSolana)===null||d===void 0?void 0:d.resources)===null||h===void 0)&&h.length?["Resources",...v.signInWithSolana.resources.map(b=>`- ${b}`)]:[]].join(`
`);const E=await _.signMessage(new TextEncoder().encode(y),"utf8");if(!E||!(E instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");w=E}}try{const{data:k,error:g}=await I(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:y,signature:Ny(w)},!((f=t.options)===null||f===void 0)&&f.captchaToken?{gotrue_meta_security:{captcha_token:(m=t.options)===null||m===void 0?void 0:m.captchaToken}}:null),xform:nt});if(g)throw g;return!k||!k.session||!k.user?{data:{user:null,session:null},error:new _s}:(k.session&&(await this._saveSession(k.session),await this._notifyAllSubscribers("SIGNED_IN",k.session)),{data:Object.assign({},k),error:g})}catch(k){if(O(k))return{data:{user:null,session:null},error:k};throw k}}async _exchangeCodeForSession(t){const n=await js(this.storage,`${this.storageKey}-code-verifier`),[r,s]=(n??"").split("/");try{const{data:i,error:o}=await I(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:r},xform:nt});if(await Es(this.storage,`${this.storageKey}-code-verifier`),o)throw o;return!i||!i.session||!i.user?{data:{user:null,session:null,redirectType:null},error:new _s}:(i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:Object.assign(Object.assign({},i),{redirectType:s??null}),error:o})}catch(i){if(O(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}}async signInWithIdToken(t){try{const{options:n,provider:r,token:s,access_token:i,nonce:o}=t,l=await I(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:s,access_token:i,nonce:o,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}},xform:nt}),{data:a,error:u}=l;return u?{data:{user:null,session:null},error:u}:!a||!a.session||!a.user?{data:{user:null,session:null},error:new _s}:(a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:u})}catch(n){if(O(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOtp(t){var n,r,s,i,o;try{if("email"in t){const{email:l,options:a}=t;let u=null,d=null;this.flowType==="pkce"&&([u,d]=await hn(this.storage,this.storageKey));const{error:h}=await I(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:l,data:(n=a==null?void 0:a.data)!==null&&n!==void 0?n:{},create_user:(r=a==null?void 0:a.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken},code_challenge:u,code_challenge_method:d},redirectTo:a==null?void 0:a.emailRedirectTo});return{data:{user:null,session:null},error:h}}if("phone"in t){const{phone:l,options:a}=t,{data:u,error:d}=await I(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:l,data:(s=a==null?void 0:a.data)!==null&&s!==void 0?s:{},create_user:(i=a==null?void 0:a.shouldCreateUser)!==null&&i!==void 0?i:!0,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken},channel:(o=a==null?void 0:a.channel)!==null&&o!==void 0?o:"sms"}});return{data:{user:null,session:null,messageId:u==null?void 0:u.message_id},error:d}}throw new ks("You must provide either an email or phone number.")}catch(l){if(O(l))return{data:{user:null,session:null},error:l};throw l}}async verifyOtp(t){var n,r;try{let s,i;"options"in t&&(s=(n=t.options)===null||n===void 0?void 0:n.redirectTo,i=(r=t.options)===null||r===void 0?void 0:r.captchaToken);const{data:o,error:l}=await I(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:i}}),redirectTo:s,xform:nt});if(l)throw l;if(!o)throw new Error("An error occurred on token verification.");const a=o.session,u=o.user;return a!=null&&a.access_token&&(await this._saveSession(a),await this._notifyAllSubscribers(t.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",a)),{data:{user:u,session:a},error:null}}catch(s){if(O(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithSSO(t){var n,r,s;try{let i=null,o=null;return this.flowType==="pkce"&&([i,o]=await hn(this.storage,this.storageKey)),await I(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:(r=(n=t.options)===null||n===void 0?void 0:n.redirectTo)!==null&&r!==void 0?r:void 0}),!((s=t==null?void 0:t.options)===null||s===void 0)&&s.captchaToken?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:o}),headers:this.headers,xform:Ky})}catch(i){if(O(i))return{data:null,error:i};throw i}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async t=>{const{data:{session:n},error:r}=t;if(r)throw r;if(!n)throw new yt;const{error:s}=await I(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:n.access_token});return{data:{user:null,session:null},error:s}})}catch(t){if(O(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const n=`${this.url}/resend`;if("email"in t){const{email:r,type:s,options:i}=t,{error:o}=await I(this.fetch,"POST",n,{headers:this.headers,body:{email:r,type:s,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}},redirectTo:i==null?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:o}}else if("phone"in t){const{phone:r,type:s,options:i}=t,{data:o,error:l}=await I(this.fetch,"POST",n,{headers:this.headers,body:{phone:r,type:s,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:o==null?void 0:o.message_id},error:l}}throw new ks("You must provide either an email or phone number and a type")}catch(n){if(O(n))return{data:{user:null,session:null},error:n};throw n}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async n=>n))}async _acquireLock(t,n){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const r=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await r,await n()))();return this.pendingInLock.push((async()=>{try{await s}catch{}})()),s}return await this.lock(`lock:${this.storageKey}`,t,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const r=n();for(this.pendingInLock.push((async()=>{try{await r}catch{}})()),await r;this.pendingInLock.length;){const s=[...this.pendingInLock];await Promise.all(s),this.pendingInLock.splice(0,s.length)}return await r}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const n=await this.__loadSession();return await t(n)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let t=null;const n=await js(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",n),n!==null&&(this._isValidSession(n)?t=n:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const r=t.expires_at?t.expires_at*1e3-Date.now()<_o:!1;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",t.expires_at),!r){if(this.storage.isServer){let o=this.suppressGetSessionWarning;t=new Proxy(t,{get:(a,u,d)=>(!o&&u==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),o=!0,this.suppressGetSessionWarning=!0),Reflect.get(a,u,d))})}return{data:{session:t},error:null}}const{session:s,error:i}=await this._callRefreshToken(t.refresh_token);return i?{data:{session:null},error:i}:{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){return t?await this._getUser(t):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(t){try{return t?await I(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:jt}):await this._useSession(async n=>{var r,s,i;const{data:o,error:l}=n;if(l)throw l;return!(!((r=o.session)===null||r===void 0)&&r.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new yt}:await I(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(i=(s=o.session)===null||s===void 0?void 0:s.access_token)!==null&&i!==void 0?i:void 0,xform:jt})})}catch(n){if(O(n))return wy(n)&&(await this._removeSession(),await Es(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:n};throw n}}async updateUser(t,n={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(t,n))}async _updateUser(t,n={}){try{return await this._useSession(async r=>{const{data:s,error:i}=r;if(i)throw i;if(!s.session)throw new yt;const o=s.session;let l=null,a=null;this.flowType==="pkce"&&t.email!=null&&([l,a]=await hn(this.storage,this.storageKey));const{data:u,error:d}=await I(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:n==null?void 0:n.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:l,code_challenge_method:a}),jwt:o.access_token,xform:jt});if(d)throw d;return o.user=u.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(r){if(O(r))return{data:{user:null},error:r};throw r}}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(t))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new yt;const n=Date.now()/1e3;let r=n,s=!0,i=null;const{payload:o}=So(t.access_token);if(o.exp&&(r=o.exp,s=r<=n),s){const{session:l,error:a}=await this._callRefreshToken(t.refresh_token);if(a)return{data:{user:null,session:null},error:a};if(!l)return{data:{user:null,session:null},error:null};i=l}else{const{data:l,error:a}=await this._getUser(t.access_token);if(a)throw a;i={access_token:t.access_token,refresh_token:t.refresh_token,user:l.user,token_type:"bearer",expires_in:r-n,expires_at:r},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(n){if(O(n))return{data:{session:null,user:null},error:n};throw n}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(t))}async _refreshSession(t){try{return await this._useSession(async n=>{var r;if(!t){const{data:o,error:l}=n;if(l)throw l;t=(r=o.session)!==null&&r!==void 0?r:void 0}if(!(t!=null&&t.refresh_token))throw new yt;const{session:s,error:i}=await this._callRefreshToken(t.refresh_token);return i?{data:{user:null,session:null},error:i}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}})}catch(n){if(O(n))return{data:{user:null,session:null},error:n};throw n}}async _getSessionFromURL(t,n){try{if(!ze())throw new Ss("No browser detected.");if(t.error||t.error_description||t.error_code)throw new Ss(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(n){case"implicit":if(this.flowType==="pkce")throw new uc("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new Ss("Not a valid implicit grant flow url.");break;default:}if(n==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new uc("No code detected.");const{data:p,error:v}=await this._exchangeCodeForSession(t.code);if(v)throw v;const _=new URL(window.location.href);return _.searchParams.delete("code"),window.history.replaceState(window.history.state,"",_.toString()),{data:{session:p.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:s,access_token:i,refresh_token:o,expires_in:l,expires_at:a,token_type:u}=t;if(!i||!l||!o||!u)throw new Ss("No session defined in URL");const d=Math.round(Date.now()/1e3),h=parseInt(l);let f=d+h;a&&(f=parseInt(a));const m=f-d;m*1e3<=mn&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${m}s, should have been closer to ${h}s`);const y=f-h;d-y>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",y,f,d):d-y<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",y,f,d);const{data:w,error:k}=await this._getUser(i);if(k)throw k;const g={provider_token:r,provider_refresh_token:s,access_token:i,expires_in:h,expires_at:f,refresh_token:o,token_type:u,user:w.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:g,redirectType:t.type},error:null}}catch(r){if(O(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(t){return!!(t.access_token||t.error_description)}async _isPKCECallback(t){const n=await js(this.storage,`${this.storageKey}-code-verifier`);return!!(t.code&&n)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(t))}async _signOut({scope:t}={scope:"global"}){return await this._useSession(async n=>{var r;const{data:s,error:i}=n;if(i)return{error:i};const o=(r=s.session)===null||r===void 0?void 0:r.access_token;if(o){const{error:l}=await this.admin.signOut(o,t);if(l&&!(yy(l)&&(l.status===404||l.status===401||l.status===403)))return{error:l}}return t!=="others"&&(await this._removeSession(),await Es(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(t){const n=Py(),r={id:n,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",n),this.stateChangeEmitters.delete(n)}};return this._debug("#onAuthStateChange()","registered callback with id",n),this.stateChangeEmitters.set(n,r),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(n)})))(),{data:{subscription:r}}}async _emitInitialSession(t){return await this._useSession(async n=>{var r,s;try{const{data:{session:i},error:o}=n;if(o)throw o;await((r=this.stateChangeEmitters.get(t))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",t,"session",i)}catch(i){await((s=this.stateChangeEmitters.get(t))===null||s===void 0?void 0:s.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",i),console.error(i)}})}async resetPasswordForEmail(t,n={}){let r=null,s=null;this.flowType==="pkce"&&([r,s]=await hn(this.storage,this.storageKey,!0));try{return await I(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:r,code_challenge_method:s,gotrue_meta_security:{captcha_token:n.captchaToken}},headers:this.headers,redirectTo:n.redirectTo})}catch(i){if(O(i))return{data:null,error:i};throw i}}async getUserIdentities(){var t;try{const{data:n,error:r}=await this.getUser();if(r)throw r;return{data:{identities:(t=n.user.identities)!==null&&t!==void 0?t:[]},error:null}}catch(n){if(O(n))return{data:null,error:n};throw n}}async linkIdentity(t){var n;try{const{data:r,error:s}=await this._useSession(async i=>{var o,l,a,u,d;const{data:h,error:f}=i;if(f)throw f;const m=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:(o=t.options)===null||o===void 0?void 0:o.redirectTo,scopes:(l=t.options)===null||l===void 0?void 0:l.scopes,queryParams:(a=t.options)===null||a===void 0?void 0:a.queryParams,skipBrowserRedirect:!0});return await I(this.fetch,"GET",m,{headers:this.headers,jwt:(d=(u=h.session)===null||u===void 0?void 0:u.access_token)!==null&&d!==void 0?d:void 0})});if(s)throw s;return ze()&&!(!((n=t.options)===null||n===void 0)&&n.skipBrowserRedirect)&&window.location.assign(r==null?void 0:r.url),{data:{provider:t.provider,url:r==null?void 0:r.url},error:null}}catch(r){if(O(r))return{data:{provider:t.provider,url:null},error:r};throw r}}async unlinkIdentity(t){try{return await this._useSession(async n=>{var r,s;const{data:i,error:o}=n;if(o)throw o;return await I(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:(s=(r=i.session)===null||r===void 0?void 0:r.access_token)!==null&&s!==void 0?s:void 0})})}catch(n){if(O(n))return{data:null,error:n};throw n}}async _refreshAccessToken(t){const n=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(n,"begin");try{const r=Date.now();return await Ly(async s=>(s>0&&await Oy(200*Math.pow(2,s-1)),this._debug(n,"refreshing attempt",s),await I(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:nt})),(s,i)=>{const o=200*Math.pow(2,s);return i&&ko(i)&&Date.now()+o-r<mn})}catch(r){if(this._debug(n,"error",r),O(r))return{data:{session:null,user:null},error:r};throw r}finally{this._debug(n,"end")}}_isValidSession(t){return typeof t=="object"&&t!==null&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,n){const r=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:n.redirectTo,scopes:n.scopes,queryParams:n.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",n,"url",r),ze()&&!n.skipBrowserRedirect&&window.location.assign(r),{data:{provider:t,url:r},error:null}}async _recoverAndRefresh(){var t;const n="#_recoverAndRefresh()";this._debug(n,"begin");try{const r=await js(this.storage,this.storageKey);if(this._debug(n,"session from storage",r),!this._isValidSession(r)){this._debug(n,"session is not valid"),r!==null&&await this._removeSession();return}const s=((t=r.expires_at)!==null&&t!==void 0?t:1/0)*1e3-Date.now()<_o;if(this._debug(n,`session has${s?"":" not"} expired with margin of ${_o}s`),s){if(this.autoRefreshToken&&r.refresh_token){const{error:i}=await this._callRefreshToken(r.refresh_token);i&&(console.error(i),ko(i)||(this._debug(n,"refresh failed with a non-retryable error, removing the session",i),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){this._debug(n,"error",r),console.error(r);return}finally{this._debug(n,"end")}}async _callRefreshToken(t){var n,r;if(!t)throw new yt;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const s=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new zi;const{data:i,error:o}=await this._refreshAccessToken(t);if(o)throw o;if(!i.session)throw new yt;await this._saveSession(i.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);const l={session:i.session,error:null};return this.refreshingDeferred.resolve(l),l}catch(i){if(this._debug(s,"error",i),O(i)){const o={session:null,error:i};return ko(i)||await this._removeSession(),(n=this.refreshingDeferred)===null||n===void 0||n.resolve(o),o}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(i),i}finally{this.refreshingDeferred=null,this._debug(s,"end")}}async _notifyAllSubscribers(t,n,r=!0){const s=`#_notifyAllSubscribers(${t})`;this._debug(s,"begin",n,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:t,session:n});const i=[],o=Array.from(this.stateChangeEmitters.values()).map(async l=>{try{await l.callback(t,n)}catch(a){i.push(a)}});if(await Promise.all(o),i.length>0){for(let l=0;l<i.length;l+=1)console.error(i[l]);throw i[0]}}finally{this._debug(s,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,await kf(this.storage,this.storageKey,t)}async _removeSession(){this._debug("#_removeSession()"),await Es(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&ze()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",t)}catch(n){console.error("removing visibilitychange callback failed",n)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),mn);this.autoRefreshTicker=t,t&&typeof t=="object"&&typeof t.unref=="function"?t.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(t),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async n=>{const{data:{session:r}}=n;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const s=Math.floor((r.expires_at*1e3-t)/mn);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts ${mn}ms, refresh threshold is ${Cl} ticks`),s<=Cl&&await this._callRefreshToken(r.refresh_token)})}catch(n){console.error("Auto refresh tick failed with error. This is likely a transient error.",n)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(t){if(t.isAcquireTimeout||t instanceof Sf)this._debug("auto refresh token tick lock not available");else throw t}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!ze()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){console.error("_handleVisibilityChange",t)}}async _onVisibilityChanged(t){const n=`#_onVisibilityChanged(${t})`;this._debug(n,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(n,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,n,r){const s=[`provider=${encodeURIComponent(n)}`];if(r!=null&&r.redirectTo&&s.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),r!=null&&r.scopes&&s.push(`scopes=${encodeURIComponent(r.scopes)}`),this.flowType==="pkce"){const[i,o]=await hn(this.storage,this.storageKey),l=new URLSearchParams({code_challenge:`${encodeURIComponent(i)}`,code_challenge_method:`${encodeURIComponent(o)}`});s.push(l.toString())}if(r!=null&&r.queryParams){const i=new URLSearchParams(r.queryParams);s.push(i.toString())}return r!=null&&r.skipBrowserRedirect&&s.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${t}?${s.join("&")}`}async _unenroll(t){try{return await this._useSession(async n=>{var r;const{data:s,error:i}=n;return i?{data:null,error:i}:await I(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(O(n))return{data:null,error:n};throw n}}async _enroll(t){try{return await this._useSession(async n=>{var r,s;const{data:i,error:o}=n;if(o)return{data:null,error:o};const l=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},t.factorType==="phone"?{phone:t.phone}:{issuer:t.issuer}),{data:a,error:u}=await I(this.fetch,"POST",`${this.url}/factors`,{body:l,headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token});return u?{data:null,error:u}:(t.factorType==="totp"&&(!((s=a==null?void 0:a.totp)===null||s===void 0)&&s.qr_code)&&(a.totp.qr_code=`data:image/svg+xml;utf-8,${a.totp.qr_code}`),{data:a,error:null})})}catch(n){if(O(n))return{data:null,error:n};throw n}}async _verify(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:s,error:i}=n;if(i)return{data:null,error:i};const{data:o,error:l}=await I(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token});return l?{data:null,error:l}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:l})})}catch(n){if(O(n))return{data:null,error:n};throw n}})}async _challenge(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:s,error:i}=n;return i?{data:null,error:i}:await I(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(O(n))return{data:null,error:n};throw n}})}async _challengeAndVerify(t){const{data:n,error:r}=await this._challenge({factorId:t.factorId});return r?{data:null,error:r}:await this._verify({factorId:t.factorId,challengeId:n.id,code:t.code})}async _listFactors(){const{data:{user:t},error:n}=await this.getUser();if(n)return{data:null,error:n};const r=(t==null?void 0:t.factors)||[],s=r.filter(o=>o.factor_type==="totp"&&o.status==="verified"),i=r.filter(o=>o.factor_type==="phone"&&o.status==="verified");return{data:{all:r,totp:s,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async t=>{var n,r;const{data:{session:s},error:i}=t;if(i)return{data:null,error:i};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=So(s.access_token);let l=null;o.aal&&(l=o.aal);let a=l;((r=(n=s.user.factors)===null||n===void 0?void 0:n.filter(h=>h.status==="verified"))!==null&&r!==void 0?r:[]).length>0&&(a="aal2");const d=o.amr||[];return{data:{currentLevel:l,nextLevel:a,currentAuthenticationMethods:d},error:null}}))}async fetchJwk(t,n={keys:[]}){let r=n.keys.find(o=>o.kid===t);if(r||(r=this.jwks.keys.find(o=>o.kid===t),r&&this.jwks_cached_at+gy>Date.now()))return r;const{data:s,error:i}=await I(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;if(!s.keys||s.keys.length===0)throw new jr("JWKS is empty");if(this.jwks=s,this.jwks_cached_at=Date.now(),r=s.keys.find(o=>o.kid===t),!r)throw new jr("No matching signing key found in JWKS");return r}async getClaims(t,n={keys:[]}){try{let r=t;if(!r){const{data:m,error:y}=await this.getSession();if(y||!m.session)return{data:null,error:y};r=m.session.access_token}const{header:s,payload:i,signature:o,raw:{header:l,payload:a}}=So(r);if(zy(i.exp),!s.kid||s.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:m}=await this.getUser(r);if(m)throw m;return{data:{claims:i,header:s,signature:o},error:null}}const u=Fy(s.alg),d=await this.fetchJwk(s.kid,n),h=await crypto.subtle.importKey("jwk",d,u,!0,["verify"]);if(!await crypto.subtle.verify(u,h,o,by(`${l}.${a}`)))throw new jr("Invalid JWT signature");return{data:{claims:i,header:s,signature:o},error:null}}catch(r){if(O(r))return{data:null,error:r};throw r}}}Wr.nextInstanceID=0;const sw=Wr;class iw extends sw{constructor(t){super(t)}}var ow=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{u(r.next(d))}catch(h){o(h)}}function a(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};class lw{constructor(t,n,r){var s,i,o;if(this.supabaseUrl=t,this.supabaseKey=n,!t)throw new Error("supabaseUrl is required.");if(!n)throw new Error("supabaseKey is required.");const l=cy(t),a=new URL(l);this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);const u=`sb-${a.hostname.split(".")[0]}-auth-token`,d={db:ny,realtime:sy,auth:Object.assign(Object.assign({},ry),{storageKey:u}),global:ty},h=dy(r??{},d);this.storageKey=(s=h.auth.storageKey)!==null&&s!==void 0?s:"",this.headers=(i=h.global.headers)!==null&&i!==void 0?i:{},h.accessToken?(this.accessToken=h.accessToken,this.auth=new Proxy({},{get:(f,m)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(m)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((o=h.auth)!==null&&o!==void 0?o:{},this.headers,h.global.fetch),this.fetch=ay(n,this._getAccessToken.bind(this),h.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},h.realtime)),this.rest=new jv(new URL("rest/v1",a).href,{headers:this.headers,schema:h.db.schema,fetch:this.fetch}),h.accessToken||this._listenForAuthEvents()}get functions(){return new ev(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new Xv(this.storageUrl.href,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,n={},r={}){return this.rest.rpc(t,n,r)}channel(t,n={config:{}}){return this.realtime.channel(t,n)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,n;return ow(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return(n=(t=r.session)===null||t===void 0?void 0:t.access_token)!==null&&n!==void 0?n:null})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:s,storageKey:i,flowType:o,lock:l,debug:a},u,d){const h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new iw({url:this.authUrl.href,headers:Object.assign(Object.assign({},h),u),storageKey:i,autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:s,flowType:o,lock:l,debug:a,fetch:d,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new Mv(this.realtimeUrl.href,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t==null?void 0:t.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((n,r)=>{this._handleTokenChanged(n,"CLIENT",r==null?void 0:r.access_token)})}_handleTokenChanged(t,n,r){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==r?this.changedAccessToken=r:t==="SIGNED_OUT"&&(this.realtime.setAuth(),n=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const aw=(e,t,n)=>new lw(e,t,n),uw="https://your-project.supabase.co",cw="your-anon-key",Nt=aw(uw,cw);function jf(){const[e,t]=x.useState([]),[n,r]=x.useState(!0),[s,i]=x.useState(null),o=async()=>{try{r(!0);const{data:h,error:f}=await Nt.from("mosques").select("*").order("created_at",{ascending:!1});if(f)throw f;const m=h.map(y=>({id:y.id,name:y.name,address:y.address||"",phone:y.phone||"",capacity:y.capacity,mushafs:y.mushafs,worshippers:y.worshippers,area:y.area,rows:y.rows,logo:y.logo_url||"",backgroundImage:y.background_image_url||"",createdAt:y.created_at}));t(m)}catch(h){i(h instanceof Error?h.message:"حدث خطأ في جلب البيانات"),console.error("Error fetching mosques:",h)}finally{r(!1)}},l=async h=>{try{const{data:f,error:m}=await Nt.from("mosques").insert([{id:h.id,name:h.name,address:h.address||null,phone:h.phone||null,capacity:h.capacity,mushafs:h.mushafs,worshippers:h.worshippers,area:h.area,rows:h.rows,logo_url:h.logo||null,background_image_url:h.backgroundImage||null}]).select().single();if(m)throw m;const y={id:f.id,name:f.name,address:f.address||"",phone:f.phone||"",capacity:f.capacity,mushafs:f.mushafs,worshippers:f.worshippers,area:f.area,rows:f.rows,logo:f.logo_url||"",backgroundImage:f.background_image_url||"",createdAt:f.created_at};return t(w=>[y,...w]),y}catch(f){throw i(f instanceof Error?f.message:"حدث خطأ في إضافة المسجد"),console.error("Error adding mosque:",f),f}},a=async(h,f)=>{try{const{data:m,error:y}=await Nt.from("mosques").update({name:f.name,address:f.address||null,phone:f.phone||null,capacity:f.capacity,mushafs:f.mushafs,worshippers:f.worshippers,area:f.area,rows:f.rows,logo_url:f.logo||null,background_image_url:f.backgroundImage||null}).eq("id",h).select().single();if(y)throw y;const w={id:m.id,name:m.name,address:m.address||"",phone:m.phone||"",capacity:m.capacity,mushafs:m.mushafs,worshippers:m.worshippers,area:m.area,rows:m.rows,logo:m.logo_url||"",backgroundImage:m.background_image_url||"",createdAt:m.created_at};return t(k=>k.map(g=>g.id===h?w:g)),w}catch(m){throw i(m instanceof Error?m.message:"حدث خطأ في تحديث المسجد"),console.error("Error updating mosque:",m),m}},u=async h=>{try{const{error:f}=await Nt.from("mosques").delete().eq("id",h);if(f)throw f;t(m=>m.filter(y=>y.id!==h))}catch(f){throw i(f instanceof Error?f.message:"حدث خطأ في حذف المسجد"),console.error("Error deleting mosque:",f),f}},d=h=>e.find(f=>f.id===h);return x.useEffect(()=>{o()},[]),{mosques:e,loading:n,error:s,addMosque:l,updateMosque:a,deleteMosque:u,getMosqueById:d,refetch:o}}const dw=()=>{const{mosques:e,loading:t,error:n,addMosque:r}=jf(),[s,i]=x.useState(""),[o,l]=x.useState(!1),[a,u]=x.useState(!1),[d,h]=x.useState({name:"",address:"",phone:""}),f=e.filter(y=>y.name.toLowerCase().includes(s.toLowerCase())||y.address&&y.address.toLowerCase().includes(s.toLowerCase())),m=async()=>{if(d.name.trim()){u(!0);try{const w={id:d.name.toLowerCase().replace(/\s+/g,"-").replace(/[^\w\-]/g,""),name:d.name,capacity:500,mushafs:150,worshippers:320,area:800,rows:25,address:d.address,phone:d.phone,logo:"",backgroundImage:""};await r(w),h({name:"",address:"",phone:""}),l(!1)}catch{alert("حدث خطأ في إضافة المسجد. حاول مرة أخرى.")}finally{u(!1)}}};return c.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-emerald-900 text-white",dir:"rtl",children:[c.jsx("header",{className:"bg-slate-800/50 backdrop-blur-lg border-b border-slate-700",children:c.jsx("div",{className:"container mx-auto px-6 py-8",children:c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"bg-emerald-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg",children:c.jsx(Bn,{className:"w-10 h-10 text-white"})}),c.jsx("h1",{className:"text-4xl font-bold mb-4",children:"نظام إعلانات المساجد الرقمي"}),c.jsx("p",{className:"text-xl text-slate-300 mb-8",children:"منصة شاملة لإدارة وعرض إعلانات المساجد"}),c.jsxs("div",{className:"max-w-md mx-auto relative",children:[c.jsx(Hg,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5"}),c.jsx("input",{type:"text",placeholder:"البحث عن مسجد...",value:s,onChange:y=>i(y.target.value),className:"w-full bg-slate-700/50 border border-slate-600 rounded-lg px-12 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-emerald-500"})]})]})})}),c.jsxs("main",{className:"container mx-auto px-6 py-12",children:[c.jsxs("div",{className:"flex justify-between items-center mb-8",children:[c.jsx("h2",{className:"text-2xl font-bold",children:"المساجد المسجلة"}),c.jsxs("button",{onClick:()=>l(!0),className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg transition-colors flex items-center space-x-2 space-x-reverse",children:[c.jsx(Xh,{className:"w-5 h-5"}),c.jsx("span",{children:"إضافة مسجد جديد"})]})]}),t&&c.jsxs("div",{className:"flex justify-center items-center py-12",children:[c.jsx(xl,{className:"w-8 h-8 animate-spin text-emerald-400"}),c.jsx("span",{className:"mr-3 text-slate-300",children:"جاري تحميل المساجد..."})]}),n&&c.jsx("div",{className:"bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6",children:c.jsxs("p",{className:"text-red-200",children:["خطأ: ",n]})}),!t&&c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:f.map(y=>c.jsxs("div",{className:"bg-slate-800/50 backdrop-blur-lg rounded-2xl p-6 shadow-2xl border border-slate-700 hover:border-emerald-500 transition-colors",children:[c.jsx("div",{className:"flex items-start justify-between mb-4",children:c.jsxs("div",{className:"flex items-center space-x-3 space-x-reverse",children:[y.logo?c.jsx("img",{src:y.logo,alt:"شعار المسجد",className:"w-12 h-12 rounded-full"}):c.jsx("div",{className:"bg-emerald-600 p-3 rounded-full",children:c.jsx(wl,{className:"w-6 h-6 text-white"})}),c.jsxs("div",{children:[c.jsx("h3",{className:"text-xl font-bold",children:y.name}),c.jsxs("p",{className:"text-slate-400 text-sm",children:["معرف: ",y.id]})]})]})}),y.address&&c.jsx("p",{className:"text-slate-300 text-sm mb-4",children:y.address}),c.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"text-emerald-400 text-lg font-bold",children:y.capacity}),c.jsx("div",{className:"text-slate-400 text-xs",children:"طاقة المصلين"})]}),c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"text-emerald-400 text-lg font-bold",children:y.worshippers}),c.jsx("div",{className:"text-slate-400 text-xs",children:"عدد المأمومين"})]})]}),c.jsxs("div",{className:"space-y-3",children:[c.jsx(hi,{to:`/${y.id}`,className:"block w-full bg-emerald-600 hover:bg-emerald-700 text-white text-center py-2 rounded-lg transition-colors",children:"عرض لوحة الإعلانات"}),c.jsx(hi,{to:`/admin/${y.id}`,className:"block w-full bg-slate-700 hover:bg-slate-600 text-white text-center py-2 rounded-lg transition-colors",children:"لوحة التحكم"})]})]},y.id))}),!t&&f.length===0&&c.jsxs("div",{className:"text-center py-12",children:[c.jsx(wl,{className:"w-16 h-16 text-slate-400 mx-auto mb-4"}),c.jsx("h3",{className:"text-xl font-bold text-slate-300 mb-2",children:"لا توجد مساجد"}),c.jsx("p",{className:"text-slate-400",children:s?"لم يتم العثور على مساجد تطابق البحث":"لم يتم إضافة أي مساجد بعد"})]}),o&&c.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:c.jsxs("div",{className:"bg-slate-800 rounded-2xl p-6 w-full max-w-md",children:[c.jsx("h3",{className:"text-xl font-bold mb-6",children:"إضافة مسجد جديد"}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"اسم المسجد *"}),c.jsx("input",{type:"text",value:d.name,onChange:y=>h({...d,name:y.target.value}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500",placeholder:"أدخل اسم المسجد"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"العنوان"}),c.jsx("input",{type:"text",value:d.address,onChange:y=>h({...d,address:y.target.value}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500",placeholder:"أدخل عنوان المسجد"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"رقم الهاتف"}),c.jsx("input",{type:"tel",value:d.phone,onChange:y=>h({...d,phone:y.target.value}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500",placeholder:"أدخل رقم الهاتف"})]})]}),c.jsxs("div",{className:"flex space-x-4 space-x-reverse mt-6",children:[c.jsx("button",{onClick:()=>l(!1),className:"flex-1 bg-slate-700 hover:bg-slate-600 text-white py-2 rounded-lg transition-colors",children:"إلغاء"}),c.jsx("button",{onClick:m,disabled:!d.name.trim()||a,className:"flex-1 bg-emerald-600 hover:bg-emerald-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white py-2 rounded-lg transition-colors flex items-center justify-center",children:a?c.jsxs(c.Fragment,{children:[c.jsx(xl,{className:"w-4 h-4 animate-spin ml-2"}),"جاري الإضافة..."]}):"إضافة"})]})]})})]}),c.jsx("footer",{className:"bg-slate-800/50 backdrop-blur-lg border-t border-slate-700 mt-12",children:c.jsx("div",{className:"container mx-auto px-6 py-8",children:c.jsxs("div",{className:"text-center text-slate-400",children:[c.jsx("p",{className:"mb-4",children:"نظام إعلانات المساجد الرقمي"}),c.jsxs("div",{className:"flex justify-center space-x-8 space-x-reverse text-sm",children:[c.jsx("span",{children:"✨ سهل الاستخدام"}),c.jsx("span",{children:"🔒 آمن ومحمي"}),c.jsx("span",{children:"📱 متجاوب مع جميع الأجهزة"})]})]})})})]})};function hw(e){const[t,n]=x.useState([]),[r,s]=x.useState(!0),[i,o]=x.useState(null),l=async()=>{if(e)try{s(!0);const{data:h,error:f}=await Nt.from("programs").select("*").eq("mosque_id",e).order("created_at",{ascending:!1});if(f)throw f;const m=h.map(y=>({id:y.id,mosqueId:y.mosque_id,name:y.name,lecturer:y.lecturer,level:y.level,attendance:y.attendance,timeFrom:y.time_from,timeTo:y.time_to,status:y.status,type:y.type,notes:y.notes,hijriDate:y.hijri_date,gregorianDate:y.gregorian_date,days:y.days}));n(m)}catch(h){o(h instanceof Error?h.message:"حدث خطأ في جلب البرامج"),console.error("Error fetching programs:",h)}finally{s(!1)}},a=async h=>{try{const{data:f,error:m}=await Nt.from("programs").insert([{mosque_id:h.mosqueId,name:h.name,lecturer:h.lecturer,level:h.level,attendance:h.attendance,time_from:h.timeFrom,time_to:h.timeTo,status:h.status,type:h.type,notes:h.notes,hijri_date:h.hijriDate,gregorian_date:h.gregorianDate,days:h.days}]).select().single();if(m)throw m;const y={id:f.id,mosqueId:f.mosque_id,name:f.name,lecturer:f.lecturer,level:f.level,attendance:f.attendance,timeFrom:f.time_from,timeTo:f.time_to,status:f.status,type:f.type,notes:f.notes,hijriDate:f.hijri_date,gregorianDate:f.gregorian_date,days:f.days};return n(w=>[y,...w]),y}catch(f){throw o(f instanceof Error?f.message:"حدث خطأ في إضافة البرنامج"),console.error("Error adding program:",f),f}},u=async(h,f)=>{try{const{data:m,error:y}=await Nt.from("programs").update({name:f.name,lecturer:f.lecturer,level:f.level,attendance:f.attendance,time_from:f.timeFrom,time_to:f.timeTo,status:f.status,type:f.type,notes:f.notes,hijri_date:f.hijriDate,gregorian_date:f.gregorianDate,days:f.days}).eq("id",h).select().single();if(y)throw y;const w={id:m.id,mosqueId:m.mosque_id,name:m.name,lecturer:m.lecturer,level:m.level,attendance:m.attendance,timeFrom:m.time_from,timeTo:m.time_to,status:m.status,type:m.type,notes:m.notes,hijriDate:m.hijri_date,gregorianDate:m.gregorian_date,days:m.days};return n(k=>k.map(g=>g.id===h?w:g)),w}catch(m){throw o(m instanceof Error?m.message:"حدث خطأ في تحديث البرنامج"),console.error("Error updating program:",m),m}},d=async h=>{try{const{error:f}=await Nt.from("programs").delete().eq("id",h);if(f)throw f;n(m=>m.filter(y=>y.id!==h))}catch(f){throw o(f instanceof Error?f.message:"حدث خطأ في حذف البرنامج"),console.error("Error deleting program:",f),f}};return x.useEffect(()=>{l()},[e]),{programs:t,loading:r,error:i,addProgram:a,updateProgram:u,deleteProgram:d,refetch:l}}const fw=()=>{const{mosqueSlug:e}=Oi(),[t,n]=x.useState(new Date),{mosques:r,loading:s,getMosqueById:i}=jf(),o=i(e||"")||{id:e||"default",name:"مسجد النور المبارك",capacity:500,mushafs:150,worshippers:320,area:800,rows:25,address:"الرياض، المملكة العربية السعودية",phone:"+966 11 123 4567",logo:"",backgroundImage:"",createdAt:new Date().toISOString()},{programs:l,loading:a}=hw(o.id);x.useEffect(()=>{const m=setInterval(()=>{n(new Date)},1e3);return()=>clearInterval(m)},[]),x.useEffect(()=>{const m=setInterval(()=>{window.location.reload()},3e4);return()=>clearInterval(m)},[]);const u=m=>m.toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}),d=m=>m.toLocaleDateString("ar-SA",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),h=m=>m.toLocaleDateString("ar-SA-u-ca-islamic",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),f=[{name:"الفجر",time:"05:15",iqama:"05:30"},{name:"الشروق",time:"06:45",iqama:""},{name:"الظهر",time:"12:15",iqama:"12:30"},{name:"العصر",time:"15:30",iqama:"15:45"},{name:"المغرب",time:"18:20",iqama:"18:25"},{name:"العشاء",time:"19:45",iqama:"20:00"}];return s?c.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-emerald-900 text-white flex items-center justify-center",dir:"rtl",children:c.jsxs("div",{className:"text-center",children:[c.jsx(xl,{className:"w-12 h-12 animate-spin text-emerald-400 mx-auto mb-4"}),c.jsx("p",{className:"text-slate-300",children:"جاري تحميل بيانات المسجد..."})]})}):c.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-emerald-900 text-white",dir:"rtl",children:[c.jsx("header",{className:"bg-slate-800/50 backdrop-blur-lg border-b border-slate-700",children:c.jsx("div",{className:"container mx-auto px-6 py-4",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-4 space-x-reverse",children:[o.logo?c.jsx("img",{src:o.logo,alt:"شعار المسجد",className:"w-12 h-12 rounded-full"}):c.jsx("div",{className:"bg-emerald-600 p-3 rounded-full",children:c.jsx(Bn,{className:"w-6 h-6"})}),c.jsxs("div",{children:[c.jsx("h1",{className:"text-2xl font-bold",children:o.name}),c.jsx("p",{className:"text-slate-300 text-sm",children:"لوحة الإعلانات الرقمية"})]})]}),c.jsxs("div",{className:"text-left",children:[c.jsx("div",{className:"text-3xl font-bold text-emerald-400",children:u(t)}),c.jsx("div",{className:"text-sm text-slate-300",children:d(t)}),c.jsx("div",{className:"text-sm text-slate-400",children:h(t)})]})]})})}),c.jsx("main",{className:"container mx-auto px-6 py-8",children:c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[c.jsxs("div",{className:"lg:col-span-2",children:[c.jsxs("div",{className:"bg-slate-800/50 backdrop-blur-lg rounded-2xl p-6 shadow-2xl border border-slate-700",children:[c.jsxs("h2",{className:"text-2xl font-bold mb-6 flex items-center",children:[c.jsx(Gh,{className:"w-6 h-6 ml-3 text-emerald-400"}),"مواقيت الصلاة"]}),c.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:f.map((m,y)=>c.jsxs("div",{className:"bg-slate-700/50 rounded-xl p-4 text-center",children:[c.jsx("h3",{className:"font-bold text-lg mb-2",children:m.name}),c.jsx("div",{className:"text-emerald-400 text-xl font-bold",children:m.time}),m.iqama&&c.jsxs("div",{className:"text-slate-300 text-sm mt-1",children:["الإقامة: ",m.iqama]})]},y))})]}),l.length>0&&c.jsxs("div",{className:"bg-slate-800/50 backdrop-blur-lg rounded-2xl p-6 shadow-2xl border border-slate-700 mt-8",children:[c.jsxs("h2",{className:"text-2xl font-bold mb-6 flex items-center",children:[c.jsx(Qh,{className:"w-6 h-6 ml-3 text-emerald-400"}),"البرامج والأنشطة"]}),c.jsx("div",{className:"space-y-4",children:l.slice(0,5).map(m=>c.jsx("div",{className:"bg-slate-700/50 rounded-xl p-4",children:c.jsxs("div",{className:"flex justify-between items-start",children:[c.jsxs("div",{children:[c.jsx("h3",{className:"font-bold text-lg",children:m.name}),c.jsxs("p",{className:"text-slate-300",children:["المحاضر: ",m.lecturer]}),c.jsx("p",{className:"text-slate-400 text-sm",children:m.notes})]}),c.jsxs("div",{className:"text-left",children:[c.jsxs("div",{className:"text-emerald-400 font-bold",children:[m.timeFrom," - ",m.timeTo]}),c.jsx("div",{className:"text-slate-400 text-sm",children:m.level})]})]})},m.id))})]})]}),c.jsxs("div",{className:"space-y-8",children:[c.jsxs("div",{className:"bg-slate-800/50 backdrop-blur-lg rounded-2xl p-6 shadow-2xl border border-slate-700",children:[c.jsxs("h2",{className:"text-xl font-bold mb-6 flex items-center",children:[c.jsx(Ag,{className:"w-5 h-5 ml-3 text-emerald-400"}),"إحصائيات المسجد"]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{className:"flex justify-between items-center",children:[c.jsx("span",{className:"text-slate-300",children:"طاقة المصلين"}),c.jsx("span",{className:"font-bold text-emerald-400",children:o.capacity})]}),c.jsxs("div",{className:"flex justify-between items-center",children:[c.jsx("span",{className:"text-slate-300",children:"عدد المأمومين"}),c.jsx("span",{className:"font-bold text-emerald-400",children:o.worshippers})]}),c.jsxs("div",{className:"flex justify-between items-center",children:[c.jsx("span",{className:"text-slate-300",children:"عدد المصاحف"}),c.jsx("span",{className:"font-bold text-emerald-400",children:o.mushafs})]}),c.jsxs("div",{className:"flex justify-between items-center",children:[c.jsx("span",{className:"text-slate-300",children:"عدد الصفوف"}),c.jsx("span",{className:"font-bold text-emerald-400",children:o.rows})]}),c.jsxs("div",{className:"flex justify-between items-center",children:[c.jsx("span",{className:"text-slate-300",children:"المساحة"}),c.jsxs("span",{className:"font-bold text-emerald-400",children:[o.area," م²"]})]})]})]}),c.jsxs("div",{className:"bg-slate-800/50 backdrop-blur-lg rounded-2xl p-6 shadow-2xl border border-slate-700",children:[c.jsxs("h2",{className:"text-xl font-bold mb-6 flex items-center",children:[c.jsx(_l,{className:"w-5 h-5 ml-3 text-emerald-400"}),"معلومات التواصل"]}),c.jsxs("div",{className:"space-y-4",children:[o.address&&c.jsxs("div",{className:"flex items-start space-x-3 space-x-reverse",children:[c.jsx(_l,{className:"w-5 h-5 text-slate-400 mt-1"}),c.jsx("span",{className:"text-slate-300",children:o.address})]}),o.phone&&c.jsxs("div",{className:"flex items-center space-x-3 space-x-reverse",children:[c.jsx(Yh,{className:"w-5 h-5 text-slate-400"}),c.jsx("span",{className:"text-slate-300",children:o.phone})]})]})]})]})]})}),c.jsx("footer",{className:"bg-slate-800/50 backdrop-blur-lg border-t border-slate-700 mt-12",children:c.jsx("div",{className:"container mx-auto px-6 py-4",children:c.jsx("div",{className:"text-center text-slate-400",children:c.jsxs("p",{children:["نظام إعلانات المسجد الرقمي - ",o.name]})})})})]})};function at(e,t){const[n,r]=x.useState(()=>{try{const i=window.localStorage.getItem(e);return i?JSON.parse(i):t}catch(i){return console.error(`Error reading localStorage key "${e}":`,i),t}});return[n,i=>{try{const o=i instanceof Function?i(n):i;r(o),window.localStorage.setItem(e,JSON.stringify(o))}catch(o){console.error(`Error setting localStorage key "${e}":`,o)}}]}const pw=()=>{const{mosqueSlug:e}=Oi(),[t,n]=x.useState(new Date),[r]=at("mosques",[]),s=r.find(d=>d.id===e),i=e||"default",[o]=at(`mosque-${i}`,s||{id:i,name:"مسجد النور المبارك",capacity:500,mushafs:150,worshippers:320,area:800,rows:25,createdAt:new Date().toISOString()}),[l]=at(`programs-${i}`,[]);x.useEffect(()=>{const d=setInterval(()=>{n(new Date)},1e3);return()=>clearInterval(d)},[]);const a=d=>d.toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!0}),u=d=>d.toLocaleDateString("ar-SA",{weekday:"long",year:"numeric",month:"long",day:"numeric"});return c.jsxs("div",{className:"p-6 min-h-screen relative",style:{background:o.backgroundImage?`linear-gradient(rgba(15, 23, 42, 0.8), rgba(15, 23, 42, 0.9)), url(${o.backgroundImage})`:"linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #065f46 100%)",backgroundSize:"cover",backgroundPosition:"center",backgroundAttachment:"fixed"},children:[c.jsx("div",{className:"bg-gradient-to-r from-emerald-600/90 to-emerald-700/90 backdrop-blur-sm rounded-xl p-6 mb-6 shadow-2xl border border-emerald-500/20",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-4 space-x-reverse",children:[o.logo&&c.jsx("img",{src:o.logo,alt:"شعار المسجد",className:"w-16 h-16 rounded-full object-cover border-2 border-white/20"}),c.jsxs("div",{className:"text-center flex-1",children:[c.jsx("h1",{className:"text-4xl font-bold text-white mb-2",children:o.name}),o.address&&c.jsxs("p",{className:"text-emerald-100 flex items-center justify-center",children:[c.jsx(_l,{className:"w-4 h-4 ml-2"}),o.address]}),o.phone&&c.jsxs("p",{className:"text-emerald-100 flex items-center justify-center mt-1",children:[c.jsx(Yh,{className:"w-4 h-4 ml-2"}),o.phone]})]})]}),c.jsxs("div",{className:"text-left",children:[c.jsxs("div",{className:"flex items-center text-emerald-100 mb-2",children:[c.jsx(Gh,{className:"w-5 h-5 ml-2"}),c.jsx("span",{className:"text-2xl font-bold",children:a(t)})]}),c.jsxs("div",{className:"flex items-center text-emerald-100",children:[c.jsx(Qh,{className:"w-5 h-5 ml-2"}),c.jsx("span",{className:"text-lg",children:u(t)})]})]})]})}),c.jsx("div",{className:"bg-red-600/90 backdrop-blur-sm text-white p-3 rounded-lg mb-6 animate-pulse border border-red-500/30",children:c.jsxs("div",{className:"flex items-center justify-center",children:[c.jsx("span",{className:"bg-red-800 text-white px-2 py-1 rounded text-sm font-bold ml-3",children:"شريط إعلامي"}),c.jsx("div",{className:"text-center font-medium",children:"تنبيه: سيتم تغيير موعد درس التفسير إلى يوم الخميس بدلاً من الأربعاء"})]})}),c.jsxs("div",{className:"grid grid-cols-2 lg:grid-cols-6 gap-4 mb-6",children:[c.jsxs("div",{className:"bg-emerald-700/80 backdrop-blur-sm rounded-lg p-4 text-center border border-emerald-600/30",children:[c.jsx("h3",{className:"text-emerald-200 text-sm mb-1",children:"إحصائيات المساجد"}),c.jsx("div",{className:"text-white font-bold text-xl",children:"1"})]}),c.jsxs("div",{className:"bg-emerald-700/80 backdrop-blur-sm rounded-lg p-4 text-center border border-emerald-600/30",children:[c.jsx("h3",{className:"text-emerald-200 text-sm mb-1",children:"عدد الصفوف"}),c.jsx("div",{className:"text-white font-bold text-xl",children:o.rows})]}),c.jsxs("div",{className:"bg-emerald-700/80 backdrop-blur-sm rounded-lg p-4 text-center border border-emerald-600/30",children:[c.jsx("h3",{className:"text-emerald-200 text-sm mb-1",children:"عدد المصاحف"}),c.jsx("div",{className:"text-white font-bold text-xl",children:o.mushafs})]}),c.jsxs("div",{className:"bg-emerald-700/80 backdrop-blur-sm rounded-lg p-4 text-center border border-emerald-600/30",children:[c.jsx("h3",{className:"text-emerald-200 text-sm mb-1",children:"عدد المأمومين"}),c.jsx("div",{className:"text-white font-bold text-xl",children:o.worshippers})]}),c.jsxs("div",{className:"bg-emerald-700/80 backdrop-blur-sm rounded-lg p-4 text-center border border-emerald-600/30",children:[c.jsx("h3",{className:"text-emerald-200 text-sm mb-1",children:"مساحة المسجد"}),c.jsx("div",{className:"text-white font-bold text-xl",children:o.area})]}),c.jsxs("div",{className:"bg-emerald-700/80 backdrop-blur-sm rounded-lg p-4 text-center border border-emerald-600/30",children:[c.jsx("h3",{className:"text-emerald-200 text-sm mb-1",children:"طاقة المصلين"}),c.jsx("div",{className:"text-white font-bold text-xl",children:o.capacity})]})]}),c.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-2xl border border-white/20",children:[c.jsxs("h2",{className:"text-2xl font-bold text-emerald-400 mb-4 text-center",children:["البرامج الحالية (",l.length,")"]}),l.length===0?c.jsxs("div",{className:"text-center text-slate-300 py-8",children:[c.jsx(Bn,{className:"w-16 h-16 mx-auto mb-4 text-slate-400"}),c.jsx("p",{className:"text-lg",children:"لا توجد برامج مضافة حالياً"}),c.jsx("p",{className:"text-sm mt-2",children:"يمكنك إضافة برامج جديدة من لوحة التحكم"})]}):c.jsx("div",{className:"overflow-x-auto",children:c.jsxs("table",{className:"w-full text-sm",children:[c.jsx("thead",{children:c.jsxs("tr",{className:"bg-emerald-600/80 text-white",children:[c.jsx("th",{className:"p-3 text-right",children:"النوع"}),c.jsx("th",{className:"p-3 text-right",children:"التاريخ الهجري"}),c.jsx("th",{className:"p-3 text-right",children:"التاريخ الميلادي"}),c.jsx("th",{className:"p-3 text-right",children:"البرنامج"}),c.jsx("th",{className:"p-3 text-right",children:"المستوى"}),c.jsx("th",{className:"p-3 text-right",children:"الحالة"}),c.jsx("th",{className:"p-3 text-right",children:"وقت البداية"}),c.jsx("th",{className:"p-3 text-right",children:"وقت النهاية"}),c.jsx("th",{className:"p-3 text-right",children:"عدد الحضور"}),c.jsx("th",{className:"p-3 text-right",children:"المحاضر"}),c.jsx("th",{className:"p-3 text-right",children:"ملاحظات"})]})}),c.jsx("tbody",{children:l.map((d,h)=>c.jsxs("tr",{className:`${h%2===0?"bg-slate-700/30":"bg-slate-600/30"} border-b border-slate-600/50 hover:bg-emerald-600/20 transition-colors`,children:[c.jsx("td",{className:"p-3 text-slate-300",children:d.type}),c.jsx("td",{className:"p-3 text-slate-300",children:d.hijriDate}),c.jsx("td",{className:"p-3 text-slate-300",children:d.gregorianDate}),c.jsx("td",{className:"p-3 text-white font-medium",children:d.name}),c.jsx("td",{className:"p-3 text-slate-300",children:d.level}),c.jsx("td",{className:"p-3",children:c.jsx("span",{className:"bg-green-600 text-white px-2 py-1 rounded text-xs",children:d.status})}),c.jsx("td",{className:"p-3 text-emerald-400 font-medium",children:d.timeFrom}),c.jsx("td",{className:"p-3 text-emerald-400 font-medium",children:d.timeTo}),c.jsx("td",{className:"p-3 text-white font-bold",children:d.attendance}),c.jsx("td",{className:"p-3 text-blue-400 font-medium",children:d.lecturer}),c.jsx("td",{className:"p-3 text-slate-300 text-xs",children:d.notes})]},d.id))})]})})]}),c.jsx("div",{className:"mt-6 text-center text-slate-300",children:c.jsxs("p",{children:["تحديث تلقائي كل 30 ثانية • آخر تحديث: ",a(t)]})})]})};function Ef(e){const[t,n]=at("currentUser",null),[r]=at("mosques",[]);return{currentUser:t,isAuthenticated:!!t,login:(l,a)=>{if(l==="admin"&&a==="admin123"){const u={id:`admin-${e||"default"}`,username:"admin",mosqueId:e||"default",role:"admin",createdAt:new Date().toISOString()};return n(u),!0}return!1},logout:()=>{n(null)},getCurrentMosque:()=>t&&r.find(l=>l.id===t.mosqueId)||null}}const yc=({currentImage:e,onImageChange:t,label:n,accept:r="image/*"})=>{const s=x.useRef(null),[i,o]=x.useState(!1),l=h=>{if(h&&h.type.startsWith("image/")){const f=new FileReader;f.onload=m=>{var w;const y=(w=m.target)==null?void 0:w.result;t(y)},f.readAsDataURL(h)}},a=h=>{h.preventDefault(),o(!1);const f=Array.from(h.dataTransfer.files);f.length>0&&l(f[0])},u=h=>{const f=h.target.files;f&&f.length>0&&l(f[0])},d=()=>{t(""),s.current&&(s.current.value="")};return c.jsxs("div",{className:"space-y-2",children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium",children:n}),e?c.jsxs("div",{className:"relative",children:[c.jsx("img",{src:e,alt:n,className:"w-full h-32 object-cover rounded-lg border border-slate-600"}),c.jsx("button",{onClick:d,className:"absolute top-2 left-2 bg-red-600 hover:bg-red-700 text-white p-1 rounded-full transition-colors",children:c.jsx(La,{className:"w-4 h-4"})})]}):c.jsxs("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer ${i?"border-emerald-500 bg-emerald-500/10":"border-slate-600 hover:border-slate-500"}`,onDrop:a,onDragOver:h=>{h.preventDefault(),o(!0)},onDragLeave:()=>o(!1),onClick:()=>{var h;return(h=s.current)==null?void 0:h.click()},children:[c.jsx(Ug,{className:"w-8 h-8 text-slate-400 mx-auto mb-2"}),c.jsx("p",{className:"text-slate-400 text-sm",children:"اسحب الصورة هنا أو انقر للاختيار"}),c.jsx("p",{className:"text-slate-500 text-xs mt-1",children:"PNG, JPG, GIF حتى 10MB"})]}),c.jsx("input",{ref:s,type:"file",accept:r,onChange:u,className:"hidden"})]})},mw=({onEmergencyAlert:e})=>{const{mosqueSlug:t}=Oi(),{currentUser:n,logout:r}=Ef(t),[s,i]=x.useState("mosque"),[o]=at("mosques",[]),l=o.find(N=>N.id===t),a=t||"default",[u,d]=at(`mosque-${a}`,l||{id:a,name:"مسجد جديد",capacity:500,mushafs:150,worshippers:320,area:800,rows:25,address:"",phone:"",createdAt:new Date().toISOString()}),[h,f]=at(`programs-${a}`,[]),[m,y]=at("mosques",[]),[w,k]=x.useState(null),[g,p]=x.useState(""),[v,_]=x.useState("announcement"),S=()=>{const N=m.map(fe=>fe.id===a?u:fe);m.find(fe=>fe.id===a)||N.push(u),y(N),alert("تم حفظ بيانات المسجد بنجاح")},E=()=>{const N={id:Date.now().toString(),mosqueId:a,name:"برنامج جديد",lecturer:"",level:"مبتدئ",attendance:0,timeFrom:"09:00",timeTo:"10:00",status:"نشط",type:"عام",notes:"",hijriDate:new Date().toLocaleDateString("ar-SA-u-ca-islamic"),gregorianDate:new Date().toLocaleDateString("ar-SA"),days:["الأحد"]};f([...h,N]),k(N)},b=N=>{k({...N})},T=()=>{if(w){const N=h.map(fe=>fe.id===w.id?w:fe);f(N),k(null),alert("تم حفظ البرنامج بنجاح")}},D=N=>{confirm("هل أنت متأكد من حذف هذا البرنامج؟")&&(f(h.filter(fe=>fe.id!==N)),alert("تم حذف البرنامج بنجاح"))},R=()=>{g.trim()&&(e(g,v),p(""),alert("تم إرسال الإعلان العاجل"))};return!n||!l?c.jsx("div",{children:"جاري التحميل..."}):c.jsxs("div",{className:"p-6 max-w-7xl mx-auto",children:[c.jsxs("div",{className:"flex justify-between items-center mb-6",children:[c.jsxs("div",{className:"flex items-center space-x-4 space-x-reverse",children:[c.jsx("div",{className:"bg-emerald-600 p-2 rounded-lg",children:c.jsx(wl,{className:"w-6 h-6 text-white"})}),c.jsxs("div",{children:[c.jsx("h1",{className:"text-2xl font-bold text-white",children:u.name}),c.jsxs("p",{className:"text-slate-400",children:["مرحباً، ",n.username]})]})]}),c.jsxs("button",{onClick:r,className:"flex items-center space-x-2 space-x-reverse bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors",children:[c.jsx(Fg,{className:"w-4 h-4"}),c.jsx("span",{children:"تسجيل الخروج"})]})]}),c.jsxs("div",{className:"flex space-x-4 space-x-reverse mb-6 border-b border-slate-700",children:[c.jsx("button",{onClick:()=>i("mosque"),className:`px-6 py-3 font-medium transition-colors ${s==="mosque"?"text-emerald-400 border-b-2 border-emerald-400":"text-slate-400 hover:text-white"}`,children:"بيانات المسجد"}),c.jsx("button",{onClick:()=>i("programs"),className:`px-6 py-3 font-medium transition-colors ${s==="programs"?"text-emerald-400 border-b-2 border-emerald-400":"text-slate-400 hover:text-white"}`,children:"إدارة البرامج"}),c.jsx("button",{onClick:()=>i("emergency"),className:`px-6 py-3 font-medium transition-colors ${s==="emergency"?"text-emerald-400 border-b-2 border-emerald-400":"text-slate-400 hover:text-white"}`,children:"الإعلانات العاجلة"})]}),s==="mosque"&&c.jsxs("div",{className:"bg-slate-800 rounded-xl p-6 shadow-xl",children:[c.jsx("h2",{className:"text-2xl font-bold text-white mb-6",children:"بيانات المسجد"}),c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6",children:[c.jsx("div",{className:"lg:col-span-1",children:c.jsx(yc,{currentImage:u.logo,onImageChange:N=>d({...u,logo:N}),label:"شعار المسجد"})}),c.jsx("div",{className:"lg:col-span-2",children:c.jsx(yc,{currentImage:u.backgroundImage,onImageChange:N=>d({...u,backgroundImage:N}),label:"صورة خلفية للمسجد"})})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"اسم المسجد"}),c.jsx("input",{type:"text",value:u.name,onChange:N=>d({...u,name:N.target.value}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"العنوان"}),c.jsx("input",{type:"text",value:u.address||"",onChange:N=>d({...u,address:N.target.value}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"رقم الهاتف"}),c.jsx("input",{type:"tel",value:u.phone||"",onChange:N=>d({...u,phone:N.target.value}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"طاقة المصلين"}),c.jsx("input",{type:"number",value:u.capacity,onChange:N=>d({...u,capacity:parseInt(N.target.value)||0}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"عدد المصاحف"}),c.jsx("input",{type:"number",value:u.mushafs,onChange:N=>d({...u,mushafs:parseInt(N.target.value)||0}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"عدد المأمومين"}),c.jsx("input",{type:"number",value:u.worshippers,onChange:N=>d({...u,worshippers:parseInt(N.target.value)||0}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"مساحة المسجد (متر مربع)"}),c.jsx("input",{type:"number",value:u.area,onChange:N=>d({...u,area:parseInt(N.target.value)||0}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"عدد الصفوف"}),c.jsx("input",{type:"number",value:u.rows,onChange:N=>d({...u,rows:parseInt(N.target.value)||0}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent"})]})]}),c.jsx("div",{className:"mt-6",children:c.jsxs("button",{onClick:S,className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2 rounded-lg transition-colors",children:[c.jsx(Bg,{className:"w-4 h-4 inline ml-2"}),"حفظ البيانات"]})})]}),s==="programs"&&c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"flex justify-between items-center",children:[c.jsx("h2",{className:"text-2xl font-bold text-white",children:"إدارة البرامج"}),c.jsxs("button",{onClick:E,className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg transition-colors",children:[c.jsx(Xh,{className:"w-4 h-4 inline ml-2"}),"إضافة برنامج"]})]}),c.jsx("div",{className:"bg-slate-800 rounded-xl p-6 shadow-xl",children:c.jsx("div",{className:"overflow-x-auto",children:c.jsxs("table",{className:"w-full text-sm",children:[c.jsx("thead",{children:c.jsxs("tr",{className:"bg-slate-700 text-slate-300",children:[c.jsx("th",{className:"p-3 text-right",children:"اسم البرنامج"}),c.jsx("th",{className:"p-3 text-right",children:"المحاضر"}),c.jsx("th",{className:"p-3 text-right",children:"المستوى"}),c.jsx("th",{className:"p-3 text-right",children:"الوقت"}),c.jsx("th",{className:"p-3 text-right",children:"الحضور"}),c.jsx("th",{className:"p-3 text-right",children:"الحالة"}),c.jsx("th",{className:"p-3 text-right",children:"الإجراءات"})]})}),c.jsx("tbody",{children:h.map(N=>c.jsxs("tr",{className:"border-b border-slate-600",children:[c.jsx("td",{className:"p-3 text-white",children:N.name}),c.jsx("td",{className:"p-3 text-slate-300",children:N.lecturer}),c.jsx("td",{className:"p-3 text-slate-300",children:N.level}),c.jsxs("td",{className:"p-3 text-slate-300",children:[N.timeFrom," - ",N.timeTo]}),c.jsx("td",{className:"p-3 text-slate-300",children:N.attendance}),c.jsx("td",{className:"p-3",children:c.jsx("span",{className:"bg-green-600 text-white px-2 py-1 rounded text-xs",children:N.status})}),c.jsx("td",{className:"p-3",children:c.jsxs("div",{className:"flex space-x-2 space-x-reverse",children:[c.jsx("button",{onClick:()=>b(N),className:"text-blue-400 hover:text-blue-300",children:c.jsx(qg,{className:"w-4 h-4"})}),c.jsx("button",{onClick:()=>D(N.id),className:"text-red-400 hover:text-red-300",children:c.jsx(Vg,{className:"w-4 h-4"})})]})})]},N.id))})]})})}),w&&c.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:c.jsxs("div",{className:"bg-slate-800 rounded-xl p-6 w-full max-w-2xl m-4 max-h-[90vh] overflow-y-auto",children:[c.jsxs("div",{className:"flex justify-between items-center mb-4",children:[c.jsx("h3",{className:"text-xl font-bold text-white",children:"تعديل البرنامج"}),c.jsx("button",{onClick:()=>k(null),className:"text-slate-400 hover:text-white",children:c.jsx(La,{className:"w-6 h-6"})})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"اسم البرنامج"}),c.jsx("input",{type:"text",value:w.name,onChange:N=>k({...w,name:N.target.value}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"المحاضر"}),c.jsx("input",{type:"text",value:w.lecturer,onChange:N=>k({...w,lecturer:N.target.value}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"المستوى"}),c.jsxs("select",{value:w.level,onChange:N=>k({...w,level:N.target.value}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500",children:[c.jsx("option",{value:"مبتدئ",children:"مبتدئ"}),c.jsx("option",{value:"متوسط",children:"متوسط"}),c.jsx("option",{value:"متقدم",children:"متقدم"}),c.jsx("option",{value:"جميع المستويات",children:"جميع المستويات"})]})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"عدد الحضور"}),c.jsx("input",{type:"number",value:w.attendance,onChange:N=>k({...w,attendance:parseInt(N.target.value)||0}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"وقت البداية"}),c.jsx("input",{type:"time",value:w.timeFrom,onChange:N=>k({...w,timeFrom:N.target.value}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"وقت النهاية"}),c.jsx("input",{type:"time",value:w.timeTo,onChange:N=>k({...w,timeTo:N.target.value}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500"})]}),c.jsxs("div",{className:"md:col-span-2",children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"ملاحظات"}),c.jsx("textarea",{value:w.notes,onChange:N=>k({...w,notes:N.target.value}),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500",rows:3})]})]}),c.jsxs("div",{className:"flex justify-end space-x-4 space-x-reverse mt-6",children:[c.jsx("button",{onClick:()=>k(null),className:"px-4 py-2 text-slate-400 hover:text-white transition-colors",children:"إلغاء"}),c.jsx("button",{onClick:T,className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2 rounded-lg transition-colors",children:"حفظ التغييرات"})]})]})})]}),s==="emergency"&&c.jsxs("div",{className:"bg-slate-800 rounded-xl p-6 shadow-xl",children:[c.jsx("h2",{className:"text-2xl font-bold text-white mb-6",children:"الإعلانات العاجلة"}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"نوع الإعلان"}),c.jsxs("select",{value:v,onChange:N=>_(N.target.value),className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500",children:[c.jsx("option",{value:"announcement",children:"إعلان عام"}),c.jsx("option",{value:"emergency",children:"حالة طوارئ"})]})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-slate-300 text-sm font-medium mb-2",children:"نص الإعلان"}),c.jsx("textarea",{value:g,onChange:N=>p(N.target.value),placeholder:"اكتب نص الإعلان العاجل هنا...",className:"w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-emerald-500",rows:4})]}),c.jsxs("button",{onClick:R,disabled:!g.trim(),className:`flex items-center px-6 py-3 rounded-lg transition-colors ${g.trim()?v==="emergency"?"bg-red-600 hover:bg-red-700":"bg-orange-600 hover:bg-orange-700":"bg-slate-600 cursor-not-allowed"} text-white`,children:[v==="emergency"?c.jsx(Kh,{className:"w-5 h-5 ml-2"}):c.jsx(Jh,{className:"w-5 h-5 ml-2"}),"إرسال الإعلان العاجل"]})]})]})]})},gw=({message:e,type:t,onClose:n})=>{const[r,s]=x.useState(15);x.useEffect(()=>{const l=setInterval(()=>{s(a=>a<=1?(n(),0):a-1)},1e3);return()=>clearInterval(l)},[n]);const i=t==="emergency"?"bg-red-600":"bg-orange-600",o=t==="emergency"?Kh:Jh;return c.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/80 animate-fadeIn",children:c.jsxs("div",{className:`${i} text-white p-8 rounded-2xl max-w-4xl mx-4 shadow-2xl animate-slideInUp`,children:[c.jsxs("div",{className:"flex justify-between items-start mb-6",children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx(o,{className:"w-8 h-8 ml-4 animate-pulse"}),c.jsx("h2",{className:"text-3xl font-bold",children:t==="emergency"?"تنبيه طوارئ":"إعلان هام"})]}),c.jsx("button",{onClick:n,className:"text-white/80 hover:text-white transition-colors",children:c.jsx(La,{className:"w-6 h-6"})})]}),c.jsxs("div",{className:"text-center",children:[c.jsx("p",{className:"text-2xl leading-relaxed mb-6 font-medium",children:e}),c.jsxs("div",{className:"flex justify-center items-center space-x-4 space-x-reverse",children:[c.jsxs("div",{className:"bg-white/20 px-4 py-2 rounded-lg",children:[c.jsx("span",{className:"text-sm",children:"سيتم إغلاق الإعلان خلال"}),c.jsx("span",{className:"text-2xl font-bold mx-2",children:r}),c.jsx("span",{className:"text-sm",children:"ثانية"})]}),c.jsx("button",{onClick:n,className:"bg-white/20 hover:bg-white/30 text-white px-6 py-2 rounded-lg transition-colors",children:"إغلاق الآن"})]})]})]})})},vw=({onLogin:e})=>{const[t,n]=x.useState(""),[r,s]=x.useState(""),[i,o]=x.useState(!1),[l,a]=x.useState(""),[u,d]=x.useState(!1),h=async f=>{f.preventDefault(),a(""),d(!0),await new Promise(y=>setTimeout(y,500)),e(t,r)||a("اسم المستخدم أو كلمة المرور غير صحيحة"),d(!1)};return c.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-emerald-900 flex items-center justify-center p-4",dir:"rtl",children:c.jsxs("div",{className:"w-full max-w-md",children:[c.jsxs("div",{className:"text-center mb-8",children:[c.jsx("div",{className:"bg-emerald-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg",children:c.jsx(Bn,{className:"w-8 h-8 text-white"})}),c.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"نظام إعلانات المسجد"}),c.jsx("p",{className:"text-slate-300",children:"تسجيل الدخول إلى لوحة التحكم"})]}),c.jsxs("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/20",children:[c.jsxs("form",{onSubmit:h,className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-slate-200 mb-2",children:"اسم المستخدم"}),c.jsx("input",{type:"text",id:"username",value:t,onChange:f=>n(f.target.value),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all",placeholder:"أدخل اسم المستخدم",required:!0,disabled:u})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-slate-200 mb-2",children:"كلمة المرور"}),c.jsxs("div",{className:"relative",children:[c.jsx("input",{type:i?"text":"password",id:"password",value:r,onChange:f=>s(f.target.value),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all pr-12",placeholder:"أدخل كلمة المرور",required:!0,disabled:u}),c.jsx("button",{type:"button",onClick:()=>o(!i),className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors",disabled:u,children:i?c.jsx(Dg,{className:"w-5 h-5"}):c.jsx(Mg,{className:"w-5 h-5"})})]})]}),l&&c.jsx("div",{className:"bg-red-500/20 border border-red-500/50 rounded-lg p-3 text-red-200 text-sm",children:l}),c.jsx("button",{type:"submit",disabled:u||!t||!r,className:"w-full bg-emerald-600 hover:bg-emerald-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2 space-x-reverse",children:u?c.jsx("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}):c.jsxs(c.Fragment,{children:[c.jsx(zg,{className:"w-5 h-5"}),c.jsx("span",{children:"تسجيل الدخول"})]})})]}),c.jsxs("div",{className:"mt-6 p-4 bg-slate-800/50 rounded-lg border border-slate-600",children:[c.jsx("p",{className:"text-slate-300 text-sm mb-2 font-medium",children:"بيانات تجريبية:"}),c.jsxs("div",{className:"text-xs text-slate-400 space-y-1",children:[c.jsxs("p",{children:["اسم المستخدم: ",c.jsx("span",{className:"text-emerald-400 font-mono",children:"admin"})]}),c.jsxs("p",{children:["كلمة المرور: ",c.jsx("span",{className:"text-emerald-400 font-mono",children:"admin123"})]})]})]})]})]})})},yw=()=>{const{mosqueSlug:e}=Oi(),{isAuthenticated:t,login:n}=Ef(e),[r,s]=x.useState("display"),[i,o]=x.useState({show:!1,message:"",type:"announcement"});return e?t?c.jsxs("div",{className:"min-h-screen bg-slate-900 text-white",dir:"rtl",children:[i.show&&c.jsx(gw,{message:i.message,type:i.type,onClose:()=>o({...i,show:!1})}),c.jsx("nav",{className:"bg-slate-800 border-b border-slate-700 px-6 py-4",children:c.jsxs("div",{className:"flex justify-between items-center",children:[c.jsxs("div",{className:"flex items-center space-x-4 space-x-reverse",children:[c.jsx("div",{className:"bg-emerald-600 p-2 rounded-lg",children:c.jsx(Bn,{className:"w-6 h-6"})}),c.jsxs("div",{children:[c.jsx("h1",{className:"text-xl font-bold",children:"لوحة تحكم المسجد"}),c.jsxs("p",{className:"text-slate-400 text-sm",children:["المسجد: ",e]})]})]}),c.jsxs("div",{className:"flex space-x-2 space-x-reverse",children:[c.jsx("button",{onClick:()=>s("display"),className:`px-4 py-2 rounded-lg transition-colors ${r==="display"?"bg-emerald-600 text-white":"bg-slate-700 text-slate-300 hover:bg-slate-600"}`,children:"لوحة العرض"}),c.jsxs("button",{onClick:()=>s("admin"),className:`px-4 py-2 rounded-lg transition-colors ${r==="admin"?"bg-emerald-600 text-white":"bg-slate-700 text-slate-300 hover:bg-slate-600"}`,children:[c.jsx(Wg,{className:"w-4 h-4 inline ml-2"}),"لوحة التحكم"]})]})]})}),c.jsx("main",{className:"flex-1",children:r==="display"?c.jsx(pw,{}):c.jsx(mw,{onEmergencyAlert:(l,a)=>o({show:!0,message:l,type:a})})})]}):c.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-emerald-900 flex items-center justify-center p-4",dir:"rtl",children:c.jsxs("div",{className:"w-full max-w-md",children:[c.jsxs("div",{className:"text-center mb-8",children:[c.jsx("div",{className:"bg-emerald-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg",children:c.jsx(Bn,{className:"w-8 h-8 text-white"})}),c.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"لوحة تحكم المسجد"}),c.jsx("p",{className:"text-slate-300",children:"تسجيل الدخول إلى لوحة التحكم"}),c.jsxs("p",{className:"text-emerald-400 text-sm mt-2",children:["المسجد: ",e]})]}),c.jsx(vw,{onLogin:n})]})}):c.jsx(X0,{to:"/",replace:!0})};function ww(){return c.jsx(jg,{children:c.jsxs(eg,{children:[c.jsx(Ms,{path:"/",element:c.jsx(dw,{})}),c.jsx(Ms,{path:"/:mosqueSlug",element:c.jsx(fw,{})}),c.jsx(Ms,{path:"/admin/:mosqueSlug",element:c.jsx(yw,{})})]})})}Rh(document.getElementById("root")).render(c.jsx(x.StrictMode,{children:c.jsx(ww,{})}));
