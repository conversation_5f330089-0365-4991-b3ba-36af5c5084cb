import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import HomePage from './pages/HomePage';
import PublicDisplay from './pages/PublicDisplay';
import AdminPage from './pages/AdminPage';

function App() {
  return (
    <Router>
      <Routes>
        {/* الصفحة الرئيسية */}
        <Route path="/" element={<HomePage />} />

        {/* صفحة العرض العام لكل مسجد */}
        <Route path="/:mosqueSlug" element={<PublicDisplay />} />

        {/* صفحة لوحة التحكم لكل مسجد */}
        <Route path="/admin/:mosqueSlug" element={<AdminPage />} />
      </Routes>
    </Router>
  );
}

export default App;