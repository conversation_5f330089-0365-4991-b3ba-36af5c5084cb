import React, { useState, useEffect } from 'react';
import { Book, Settings } from 'lucide-react';
import DisplayBoard from './components/DisplayBoard';
import AdminPanel from './components/AdminPanel';
import EmergencyAlert from './components/EmergencyAlert';
import LoginForm from './components/LoginForm';
import { useAuth } from './hooks/useAuth';

function App() {
  const { isAuthenticated, login } = useAuth();
  const [currentView, setCurrentView] = useState<'display' | 'admin'>('display');
  const [emergencyAlert, setEmergencyAlert] = useState<{
    show: boolean;
    message: string;
    type: 'emergency' | 'announcement';
  }>({
    show: false,
    message: '',
    type: 'announcement'
  });

  // Auto-refresh display every 30 seconds
  useEffect(() => {
    if (currentView === 'display') {
      const interval = setInterval(() => {
        // Force re-render to update times and data
        setCurrentView('display');
      }, 30000);
      return () => clearInterval(interval);
    }
  }, [currentView]);

  // Sample emergency alert for demo (remove in production)
  useEffect(() => {
    if (isAuthenticated) {
      const timer = setTimeout(() => {
        setEmergencyAlert({
          show: true,
          message: 'إعلان هام: سيتم إقامة صلاة الاستسقاء غداً الجمعة بعد صلاة الفجر',
          type: 'announcement'
        });
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated]);

  // Show login form if not authenticated
  if (!isAuthenticated) {
    return <LoginForm onLogin={login} />;
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white" dir="rtl">
      {/* Emergency Alert Overlay */}
      {emergencyAlert.show && (
        <EmergencyAlert
          message={emergencyAlert.message}
          type={emergencyAlert.type}
          onClose={() => setEmergencyAlert({ ...emergencyAlert, show: false })}
        />
      )}

      {/* Navigation Bar */}
      <nav className="bg-slate-800 border-b border-slate-700 px-6 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="bg-emerald-600 p-2 rounded-lg">
              <Book className="w-6 h-6" />
            </div>
            <h1 className="text-xl font-bold">نظام إعلانات المسجد</h1>
          </div>
          <div className="flex space-x-2 space-x-reverse">
            <button
              onClick={() => setCurrentView('display')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                currentView === 'display' 
                  ? 'bg-emerald-600 text-white' 
                  : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
              }`}
            >
              لوحة العرض
            </button>
            <button
              onClick={() => setCurrentView('admin')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                currentView === 'admin' 
                  ? 'bg-emerald-600 text-white' 
                  : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
              }`}
            >
              <Settings className="w-4 h-4 inline ml-2" />
              لوحة التحكم
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex-1">
        {currentView === 'display' ? (
          <DisplayBoard />
        ) : (
          <AdminPanel 
            onEmergencyAlert={(message, type) => 
              setEmergencyAlert({ show: true, message, type })
            }
          />
        )}
      </main>
    </div>
  );
}

export default App;