import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Calendar, Clock, Book, MapPin, Phone, Users, Activity, Loader } from 'lucide-react';
import { useMosques } from '../hooks/useMosques';
import { usePrograms } from '../hooks/usePrograms';
import { Program, Mosque } from '../types';

const PublicDisplay: React.FC = () => {
  const { mosqueSlug } = useParams<{ mosqueSlug: string }>();
  const [currentTime, setCurrentTime] = useState(new Date());

  // البحث عن المسجد بناءً على الـ slug
  const { mosques, loading: mosquesLoading, getMosqueById } = useMosques();
  const currentMosque = getMosqueById(mosqueSlug || '') || {
    id: mosqueSlug || 'default',
    name: 'مسجد النور المبارك',
    capacity: 500,
    mushafs: 150,
    worshippers: 320,
    area: 800,
    rows: 25,
    address: 'الرياض، المملكة العربية السعودية',
    phone: '+966 11 123 4567',
    logo: '',
    backgroundImage: '',
    createdAt: new Date().toISOString()
  };

  const { programs, loading: programsLoading } = usePrograms(currentMosque.id);

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      window.location.reload();
    }, 30000);
    return () => clearInterval(interval);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('ar-SA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatHijriDate = (date: Date) => {
    return date.toLocaleDateString('ar-SA-u-ca-islamic', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const prayerTimes = [
    { name: 'الفجر', time: '05:15', iqama: '05:30' },
    { name: 'الشروق', time: '06:45', iqama: '' },
    { name: 'الظهر', time: '12:15', iqama: '12:30' },
    { name: 'العصر', time: '15:30', iqama: '15:45' },
    { name: 'المغرب', time: '18:20', iqama: '18:25' },
    { name: 'العشاء', time: '19:45', iqama: '20:00' }
  ];

  // Loading state
  if (mosquesLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-emerald-900 text-white flex items-center justify-center" dir="rtl">
        <div className="text-center">
          <Loader className="w-12 h-12 animate-spin text-emerald-400 mx-auto mb-4" />
          <p className="text-slate-300">جاري تحميل بيانات المسجد...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-emerald-900 text-white" dir="rtl">
      {/* Header */}
      <header className="bg-slate-800/50 backdrop-blur-lg border-b border-slate-700">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              {currentMosque.logo ? (
                <img src={currentMosque.logo} alt="شعار المسجد" className="w-12 h-12 rounded-full" />
              ) : (
                <div className="bg-emerald-600 p-3 rounded-full">
                  <Book className="w-6 h-6" />
                </div>
              )}
              <div>
                <h1 className="text-2xl font-bold">{currentMosque.name}</h1>
                <p className="text-slate-300 text-sm">لوحة الإعلانات الرقمية</p>
              </div>
            </div>
            
            <div className="text-left">
              <div className="text-3xl font-bold text-emerald-400">{formatTime(currentTime)}</div>
              <div className="text-sm text-slate-300">{formatDate(currentTime)}</div>
              <div className="text-sm text-slate-400">{formatHijriDate(currentTime)}</div>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Prayer Times */}
          <div className="lg:col-span-2">
            <div className="bg-slate-800/50 backdrop-blur-lg rounded-2xl p-6 shadow-2xl border border-slate-700">
              <h2 className="text-2xl font-bold mb-6 flex items-center">
                <Clock className="w-6 h-6 ml-3 text-emerald-400" />
                مواقيت الصلاة
              </h2>
              
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {prayerTimes.map((prayer, index) => (
                  <div key={index} className="bg-slate-700/50 rounded-xl p-4 text-center">
                    <h3 className="font-bold text-lg mb-2">{prayer.name}</h3>
                    <div className="text-emerald-400 text-xl font-bold">{prayer.time}</div>
                    {prayer.iqama && (
                      <div className="text-slate-300 text-sm mt-1">
                        الإقامة: {prayer.iqama}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Programs */}
            {programs.length > 0 && (
              <div className="bg-slate-800/50 backdrop-blur-lg rounded-2xl p-6 shadow-2xl border border-slate-700 mt-8">
                <h2 className="text-2xl font-bold mb-6 flex items-center">
                  <Calendar className="w-6 h-6 ml-3 text-emerald-400" />
                  البرامج والأنشطة
                </h2>
                
                <div className="space-y-4">
                  {programs.slice(0, 5).map((program) => (
                    <div key={program.id} className="bg-slate-700/50 rounded-xl p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-bold text-lg">{program.name}</h3>
                          <p className="text-slate-300">المحاضر: {program.lecturer}</p>
                          <p className="text-slate-400 text-sm">{program.notes}</p>
                        </div>
                        <div className="text-left">
                          <div className="text-emerald-400 font-bold">
                            {program.timeFrom} - {program.timeTo}
                          </div>
                          <div className="text-slate-400 text-sm">{program.level}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Mosque Info */}
          <div className="space-y-8">
            {/* Mosque Stats */}
            <div className="bg-slate-800/50 backdrop-blur-lg rounded-2xl p-6 shadow-2xl border border-slate-700">
              <h2 className="text-xl font-bold mb-6 flex items-center">
                <Activity className="w-5 h-5 ml-3 text-emerald-400" />
                إحصائيات المسجد
              </h2>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">طاقة المصلين</span>
                  <span className="font-bold text-emerald-400">{currentMosque.capacity}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">عدد المأمومين</span>
                  <span className="font-bold text-emerald-400">{currentMosque.worshippers}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">عدد المصاحف</span>
                  <span className="font-bold text-emerald-400">{currentMosque.mushafs}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">عدد الصفوف</span>
                  <span className="font-bold text-emerald-400">{currentMosque.rows}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">المساحة</span>
                  <span className="font-bold text-emerald-400">{currentMosque.area} م²</span>
                </div>
              </div>
            </div>

            {/* Contact Info */}
            <div className="bg-slate-800/50 backdrop-blur-lg rounded-2xl p-6 shadow-2xl border border-slate-700">
              <h2 className="text-xl font-bold mb-6 flex items-center">
                <MapPin className="w-5 h-5 ml-3 text-emerald-400" />
                معلومات التواصل
              </h2>
              
              <div className="space-y-4">
                {currentMosque.address && (
                  <div className="flex items-start space-x-3 space-x-reverse">
                    <MapPin className="w-5 h-5 text-slate-400 mt-1" />
                    <span className="text-slate-300">{currentMosque.address}</span>
                  </div>
                )}
                {currentMosque.phone && (
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <Phone className="w-5 h-5 text-slate-400" />
                    <span className="text-slate-300">{currentMosque.phone}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-slate-800/50 backdrop-blur-lg border-t border-slate-700 mt-12">
        <div className="container mx-auto px-6 py-4">
          <div className="text-center text-slate-400">
            <p>نظام إعلانات المسجد الرقمي - {currentMosque.name}</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PublicDisplay;
