import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { Mosque } from '../types';

export function useMosques() {
  const [mosques, setMosques] = useState<Mosque[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // جلب جميع المساجد
  const fetchMosques = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('mosques')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      const formattedMosques: Mosque[] = data.map(mosque => ({
        id: mosque.id,
        name: mosque.name,
        address: mosque.address || '',
        phone: mosque.phone || '',
        capacity: mosque.capacity,
        mushafs: mosque.mushafs,
        worshippers: mosque.worshippers,
        area: mosque.area,
        rows: mosque.rows,
        logo: mosque.logo_url || '',
        backgroundImage: mosque.background_image_url || '',
        createdAt: mosque.created_at
      }));

      setMosques(formattedMosques);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ في جلب البيانات');
      console.error('Error fetching mosques:', err);
    } finally {
      setLoading(false);
    }
  };

  // إضافة مسجد جديد
  const addMosque = async (mosque: Omit<Mosque, 'createdAt'>) => {
    try {
      const { data, error } = await supabase
        .from('mosques')
        .insert([{
          id: mosque.id,
          name: mosque.name,
          address: mosque.address || null,
          phone: mosque.phone || null,
          capacity: mosque.capacity,
          mushafs: mosque.mushafs,
          worshippers: mosque.worshippers,
          area: mosque.area,
          rows: mosque.rows,
          logo_url: mosque.logo || null,
          background_image_url: mosque.backgroundImage || null
        }])
        .select()
        .single();

      if (error) throw error;

      const newMosque: Mosque = {
        id: data.id,
        name: data.name,
        address: data.address || '',
        phone: data.phone || '',
        capacity: data.capacity,
        mushafs: data.mushafs,
        worshippers: data.worshippers,
        area: data.area,
        rows: data.rows,
        logo: data.logo_url || '',
        backgroundImage: data.background_image_url || '',
        createdAt: data.created_at
      };

      setMosques(prev => [newMosque, ...prev]);
      return newMosque;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ في إضافة المسجد');
      console.error('Error adding mosque:', err);
      throw err;
    }
  };

  // تحديث مسجد
  const updateMosque = async (id: string, updates: Partial<Mosque>) => {
    try {
      const { data, error } = await supabase
        .from('mosques')
        .update({
          name: updates.name,
          address: updates.address || null,
          phone: updates.phone || null,
          capacity: updates.capacity,
          mushafs: updates.mushafs,
          worshippers: updates.worshippers,
          area: updates.area,
          rows: updates.rows,
          logo_url: updates.logo || null,
          background_image_url: updates.backgroundImage || null
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      const updatedMosque: Mosque = {
        id: data.id,
        name: data.name,
        address: data.address || '',
        phone: data.phone || '',
        capacity: data.capacity,
        mushafs: data.mushafs,
        worshippers: data.worshippers,
        area: data.area,
        rows: data.rows,
        logo: data.logo_url || '',
        backgroundImage: data.background_image_url || '',
        createdAt: data.created_at
      };

      setMosques(prev => prev.map(m => m.id === id ? updatedMosque : m));
      return updatedMosque;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ في تحديث المسجد');
      console.error('Error updating mosque:', err);
      throw err;
    }
  };

  // حذف مسجد
  const deleteMosque = async (id: string) => {
    try {
      const { error } = await supabase
        .from('mosques')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setMosques(prev => prev.filter(m => m.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ في حذف المسجد');
      console.error('Error deleting mosque:', err);
      throw err;
    }
  };

  // البحث عن مسجد بالمعرف
  const getMosqueById = (id: string): Mosque | undefined => {
    return mosques.find(m => m.id === id);
  };

  useEffect(() => {
    fetchMosques();
  }, []);

  return {
    mosques,
    loading,
    error,
    addMosque,
    updateMosque,
    deleteMosque,
    getMosqueById,
    refetch: fetchMosques
  };
}
